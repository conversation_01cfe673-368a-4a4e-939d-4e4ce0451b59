use std::sync::Mutex;

use bevy::core_pipeline::tonemapping::Tonemapping;
use bevy::prelude::*;
use bevy_hanabi::ParticleEffect;
use crossbeam_channel::{Receiver, Sender};
use protobuf::{Message, ProtobufEnum};
use rustc_hash::FxHashMap;

use pianorhythm_proto::midi_renditions::MidiNoteSource;
use pianorhythm_proto::pianorhythm_actions::{
    AppStateActions, AppStateActions_Action, AudioSynthActionData, AudioSynthActions, AudioSynthActions_Action
};
use pianorhythm_proto::pianorhythm_app_renditions::{GraphicShadowFilteringMethod, GraphicsMsaaSamples, GraphicsPresets};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::user_renditions::{AvatarWorldDataDto_AvatarMessageWorldPosition, UserClientDto};
use pianorhythm_shared::midi::DRUM_CHANNEL;
use pianorhythm_shared::util::hash_string_to_u32;
use pianorhythm_shared::GENERAL_MIDI;
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::JsValue;

use crate::components::{InstrumentType, LowPolyModel, MainInstrument, StagePart};
use crate::plugins::piano::resources::LastMouseNoteOn;
use crate::plugins::piano::{PianoPluginSystemID2, PianoPluginSystems2};
use crate::resources;
use crate::resources::{ActiveStageSettings, ClientSocketIDStr, CoreSystems, CoreSystemsID, DrumsDisplayed};
use crate::types::PianoRhythmSynthEvent;

#[derive(Debug, Clone, Reflect, Event)]
#[reflect(Debug)]
pub struct UserNoteData {
    pub socket_id: Option<u32>,
    pub channel: u8,
    pub note: u8,
    pub vel: u8,
    #[reflect(ignore)]
    pub raw_synth_event: Option<PianoRhythmSynthEvent>,
    #[reflect(ignore)]
    pub source: MidiNoteSource,
    #[reflect(ignore)]
    pub gm_instrument: Option<GENERAL_MIDI::GMSoundSet>,
}

impl Default for UserNoteData {
    fn default() -> Self {
        Self {
            socket_id: default(),
            channel: default(),
            note: default(),
            vel: default(),
            source: MidiNoteSource::MIDI,
            gm_instrument: None,
            raw_synth_event: None,
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum ECSWorldAction {
    ResetCamera,
    AppEffect(AppStateEffects),
    AppEvent(AppStateEvents),
    AppAction(AppStateActions),
}

impl Into<ECSWorldActions> for ECSWorldAction {
    fn into(self) -> ECSWorldActions {
        ECSWorldActions::new(self)
    }
}

#[derive(Debug, Clone, Event)]
pub enum SynthEventsBroadcastAction {
    NoteOn(UserNoteData),
    NoteOff(UserNoteData),
}

#[derive(Debug, Clone, Event)]
pub enum AvatarEventsBroadcastAction {
    SetPosition { x: f32, y: f32, z: f32 },
    SetPianoBench(u8),
}

#[derive(Debug, Clone, Event)]
pub enum ECSSynthEventsAction {
    NoteOn(UserNoteData),
    NoteOff(UserNoteData),
    SustainPedal(UserNoteData),
    SoftPedal(UserNoteData),
    AllNoteOff(u8),
}

#[derive(Event, Debug, Clone)]
pub struct ECSSynthEvents(pub ECSSynthEventsAction, pub GENERAL_MIDI::GMSoundSet);

impl ECSSynthEvents {
    pub fn new(action: ECSSynthEventsAction, inst: GENERAL_MIDI::GMSoundSet) -> Self {
        Self(action, inst)
    }
}

#[derive(Event, Debug, Clone, PartialEq)]
pub struct ECSWorldActions {
    pub message: ECSWorldAction,
}

impl ECSWorldActions {
    pub fn new(action: ECSWorldAction) -> Self {
        Self { message: action }
    }
}

#[derive(Event, Debug, Clone, PartialEq)]
pub struct ECSWorldEffects {
    pub message: AppStateEffects,
}

impl ECSWorldEffects {
    pub fn new(action: AppStateEffects_Action) -> Self {
        let mut effect = AppStateEffects::new();
        effect.set_action(action);
        Self { message: effect }
    }
}

#[derive(Event, Debug, Clone, PartialEq)]
pub struct ECSWorldEvents {
    pub message: AppStateEvents,
}

impl ECSWorldEvents {
    pub fn new(event: AppStateEvents) -> Self {
        Self { message: event }
    }
}

#[derive(Resource, Deref, DerefMut)]
pub struct ChannelReceiver<T>(pub Mutex<Receiver<T>>);

#[derive(Resource, Deref, DerefMut)]
pub struct ChannelSender<T>(pub Sender<T>);

pub trait AppExtensions {
    // Allows you to create bevy events using mpsc Sender
    fn add_event_channel<T: Event>(&mut self, sender: Sender<T>, receiver: Receiver<T>) -> &mut Self;
}

impl AppExtensions for App {
    fn add_event_channel<T: Event>(&mut self, sender: Sender<T>, receiver: Receiver<T>) -> &mut Self {
        assert!(!self.world().contains_resource::<ChannelReceiver<T>>(), "this event channel is already initialized",);

        self.add_event::<T>();
        self.add_systems(First, channel_to_event::<T>);
        self.insert_resource(ChannelSender(sender));
        self.insert_resource(ChannelReceiver(Mutex::new(receiver)));
        self
    }
}

fn channel_to_event<T: 'static + Send + Sync + Event>(receiver: Res<ChannelReceiver<T>>, mut writer: EventWriter<T>) {
    // this should be the only system working with the receiver,
    // thus we always expect to get this lock
    let events = receiver.0.lock().expect("unable to acquire mutex lock");
    writer.write_batch(events.try_iter());
}

// ------------------------------------------------------------------- //
#[cfg(target_arch = "wasm32")]
#[cfg(not(feature = "desktop"))]
/// In WASM, broadcasts app state effects to the outside world.
pub fn broadcasting_effects_to_app(mut events: EventReader<ECSWorldEffects>) {
    for event in events.read() {
        let app_state_effect = &event.message;
        let bytes = app_state_effect.write_to_bytes().unwrap_or_default();
        let js_output = &JsValue::from(js_sys::Uint8Array::from(&bytes[..]));
        crate::utils::emit_custom_event("renderer_effects", &js_output);
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop")]
pub fn broadcasting_effects_to_app(mut events: EventReader<ECSWorldEffects>) {
    for event in events.read() {
        let app_state_effect = &event.message;
        unsafe {
            let Some(broadcast) = crate::utils::desktop_ffi::BROADCAST_APP_EFFECTS_EVENT_CHANNEL.get() else {
                return;
            };
            _ = broadcast.0.try_send(app_state_effect.clone());
        }
    }
}

#[cfg(target_arch = "wasm32")]
#[cfg(not(feature = "desktop"))]
/// Broadcasts app state events to the outside world.
pub fn broadcasting_events_to_app(mut events: EventReader<ECSWorldEvents>) {
    for event in events.read() {
        let app_state_event = &event.message;
        let js_output = &JsValue::from(app_state_event.value());
        crate::utils::emit_custom_event("renderer_events", &js_output);
    }
}

#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop")]
pub fn broadcasting_events_to_app(mut events: EventReader<ECSWorldEvents>) {
    for event in events.read() {
        let app_state_event = &event.message;
        unsafe {
            let Some(broadcast) = crate::utils::desktop_ffi::BROADCAST_APP_EVENTS_EVENT_CHANNEL.get() else {
                return;
            };
            _ = broadcast.0.try_send(vec![app_state_event.clone()]);
        }
    }
}

pub fn receiving_app_effects(
    mut commands: Commands, mut audio_channels: ResMut<resources::SynthAudioChannels>,
    mut audio_channels_active_state: ResMut<resources::SynthAudioChannelsActiveState>, mut users: ResMut<resources::Users>,
    mut user_colors: ResMut<resources::UserColors>, mut event_reader: EventReader<ECSWorldActions>, systems2: Res<PianoPluginSystems2>,
    is_mobile: Res<resources::ClientIsMobile>,
) {
    for my_event in event_reader.read() {
        match &my_event.message {
            ECSWorldAction::AppEffect(effect) => {
                match effect.action {
                    AppStateEffects_Action::UpdateUser | AppStateEffects_Action::AddUser if effect.has_clientSideUserDto() => {
                        let value = effect.get_clientSideUserDto().to_owned();

                        if !value.has_socketIDHashed() {
                            return;
                        }

                        let socket_id = value.get_socketIDHashed();
                        user_colors.0.insert(socket_id, resources::UserColors::create_color(value.get_userDto()));
                        users.0.insert(socket_id, value);
                    }
                    AppStateEffects_Action::RemoveUser => {
                        let value = effect.get_socketId();
                        users.0.retain(|x, _| *x != hash_string_to_u32(value.to_string()));
                        user_colors.0.retain(|x, _| *x != (hash_string_to_u32(value.to_string())));
                    }
                    AppStateEffects_Action::UsersSet if effect.has_clientSideUserDtoList() => {
                        let input = effect.get_clientSideUserDtoList().get_list();

                        // Set Users list
                        let mut hmap = FxHashMap::default();
                        for user in input.iter().filter(|x| x.has_socketIDHashed()) {
                            hmap.insert(user.get_socketIDHashed(), user.clone());
                        }
                        commands.insert_resource(resources::Users(hmap));

                        // Set colors
                        let mut hmap = FxHashMap::default();
                        for user in input.iter().filter(|x| x.has_socketIDHashed()) {
                            hmap.insert(user.get_socketIDHashed(), resources::UserColors::create_color(user.get_userDto()));
                        }
                        commands.insert_resource(resources::UserColors(hmap));
                    }
                    AppStateEffects_Action::SynthSoundfontFailedToLoad => {}
                    AppStateEffects_Action::SynthChannelUpdated if effect.has_audioChannel() => {
                        let audio_channel = effect.get_audioChannel();
                        let channel = audio_channel.get_channel();

                        if audio_channels.0.get(&channel) != Some(audio_channel) {
                            audio_channels.0.insert(channel, audio_channel.to_owned());
                        }

                        let active = audio_channel.get_active();

                        if audio_channels_active_state.0.get(&channel).cloned() != Some(active) {
                            audio_channels_active_state.0.insert(channel, active);
                            commands.run_system(systems2.0[&PianoPluginSystemID2::OnSynthChannelUpdate]);
                        }
                    }
                    AppStateEffects_Action::SetSlotMode if effect.has_slotMode() => {
                        commands.insert_resource(resources::ChannelsMode(effect.get_slotMode()));
                        commands.run_system(systems2.0[&PianoPluginSystemID2::OnChannelsModeChange]);
                    }
                    AppStateEffects_Action::SetPrimaryChannel if effect.has_uint32Value() => {
                        commands.insert_resource(resources::PrimaryAudioChannel(effect.get_uint32Value() as u8));
                    }
                    AppStateEffects_Action::OnWelcome | AppStateEffects_Action::ClientUpdated => {
                        let mut user_dto_o: Option<UserClientDto> = None;

                        if effect.has_userClientDto() {
                            user_dto_o = Some(effect.get_userClientDto().clone());
                        } else if effect.has_welcomeDto() {
                            user_dto_o = Some(effect.get_welcomeDto().get_userClientDto().clone());
                        }

                        if let Some(user_dto) = user_dto_o {
                            let socket_id_str = user_dto.get_userDto().get_socketID();
                            let socket_id = hash_string_to_u32(socket_id_str.to_string());

                            commands.insert_resource(resources::Client(user_dto.clone()));
                            commands.insert_resource(resources::ClientSocketID(socket_id));
                            commands.insert_resource(resources::ClientSocketIDStr(socket_id_str.to_string()));
                        }
                    }
                    AppStateEffects_Action::LoadRoomStage if effect.has_loadRoomStageDetails() => {
                        commands.insert_resource(resources::RoomStage(effect.get_loadRoomStageDetails().clone()));
                    }
                    AppStateEffects_Action::AppSettingsUpdated if effect.has_appSettings() => {
                        let mut settings = effect.get_appSettings().clone();
                        if !settings.is_initialized() {
                            break;
                        }

                        // Forcefully disable certain effects on mobile
                        if is_mobile.0 {
                            settings.GRAPHICS_PRESET = GraphicsPresets::Preset_Low;
                        }

                        commands.insert_resource(resources::ChannelsMode(settings.get_SLOT_MODE()));
                        commands.insert_resource(resources::ClientAppSettings(settings));
                    }
                    AppStateEffects_Action::JoinedRoomSuccess if effect.has_joinedRoomData() => {
                        commands.insert_resource(crate::resources::CurrentRoomType(effect.get_joinedRoomData().get_roomType()));
                    }
                    _ => {}
                }
            }
            _ => {}
        }
    }
}

pub fn receiving_app_actions(mut commands: Commands, mut event_reader: EventReader<ECSWorldActions>, systems: Res<CoreSystems>) {
    for my_event in event_reader.read() {
        match &my_event.message {
            ECSWorldAction::AppAction(action) => match action.action {
                AppStateActions_Action::SetIsMobile if action.has_boolValue() => {
                    commands.insert_resource(resources::ClientIsMobile(action.get_boolValue()));
                }
                AppStateActions_Action::RendererToggleLockCamera => {
                    commands.run_system(systems.0[&CoreSystemsID::ToggleCameraLock]);
                }
                AppStateActions_Action::RendererResetCamera => {
                    commands.run_system(systems.0[&CoreSystemsID::ResetCamera]);
                }
                AppStateActions_Action::RendererSetCameraTopPosition => {
                    commands.run_system(systems.0[&CoreSystemsID::SetCameraTopPosition]);
                }
                AppStateActions_Action::RendererSetKeyboardMappings if action.has_keyboardVisualizeMappings() => {
                    commands.insert_resource(resources::KeysInputMapping(action.get_keyboardVisualizeMappings().clone()));
                }
                AppStateActions_Action::RendererToggleDisplayKeyboardMappings if action.has_boolValue() => {
                    commands.insert_resource(resources::DisplayKeysInputMapping(action.get_boolValue()));
                }
                AppStateActions_Action::RendererToggleDisplayKeyboardMappings => {
                    commands.run_system(systems.0[&CoreSystemsID::ToggleDisplayKeyInputMapping]);
                }
                _ => {}
            },
            _ => {}
        }
    }
}

pub fn handle_synth_actions(mut event_reader: EventReader<ECSSynthEventsAction>, mut event_writer: EventWriter<ECSSynthEvents>) {
    for event in event_reader.read() {
        let get_gm_inst = |channel: u8, synth_event: Option<PianoRhythmSynthEvent>| {
            // Force drum channel instruments to drum GM
            if channel == DRUM_CHANNEL {
                return GENERAL_MIDI::GMSoundSet::SynthDrum;
            }

            synth_event
                .and_then(|evt| evt.current_program)
                .and_then(GENERAL_MIDI::get_gm_set_from_program)
                .unwrap_or(GENERAL_MIDI::GMSoundSet::AcousticGrandPiano)
        };

        match event {
            ECSSynthEventsAction::NoteOn(data) => {
                event_writer.write(ECSSynthEvents::new(event.clone(), get_gm_inst(data.channel, data.raw_synth_event.clone())));
            }
            ECSSynthEventsAction::NoteOff(data) => {
                event_writer.write(ECSSynthEvents::new(event.clone(), get_gm_inst(data.channel, data.raw_synth_event.clone())));
            }
            ECSSynthEventsAction::SustainPedal(data) => {
                event_writer.write(ECSSynthEvents::new(event.clone(), get_gm_inst(data.channel, data.raw_synth_event.clone())));
            }
            ECSSynthEventsAction::SoftPedal(data) => {
                event_writer.write(ECSSynthEvents::new(event.clone(), get_gm_inst(data.channel, data.raw_synth_event.clone())));
            }
            ECSSynthEventsAction::AllNoteOff(_) => {
                event_writer.write(ECSSynthEvents::new(event.clone(), GENERAL_MIDI::GMSoundSet::default()));
            }
        }
    }
}

pub fn on_app_setting_change(
    mut commands: Commands,
    app_settings: Res<resources::ClientAppSettings>,
    _active_stage_settings: Res<ActiveStageSettings>,
    drums_displayed: Res<resources::DrumsDisplayed>,
    // Cameras
    mut view_targets: Query<(Entity, &mut Camera, &mut Tonemapping, &mut Msaa), With<crate::components::MainCamera>>,
    mut set: ParamSet<(
        // lights for shadows/visibility
        Query<(&mut PointLight, &mut Visibility), With<StagePart>>,
        // particle effects
        Query<&mut Visibility, With<ParticleEffect>>,
        // general stage parts (excluding lights)
        Query<&mut Visibility, (With<StagePart>, Without<PointLight>)>,
        // instruments
        Query<(&mut Visibility, &MainInstrument, Option<&LowPolyModel>)>,
    )>,
) {
    let setting = app_settings.0.clone();
    if !setting.is_initialized() {
        return;
    }

    // Set shadows/visibility for point lights
    for (mut point_light, mut visibility) in set.p0().iter_mut() {
        point_light.shadows_enabled = setting.GRAPHICS_ENABLE_SHADOWS;
        update_visibility(&mut visibility, setting.GRAPHICS_ENABLE_LIGHTS);
    }

    // Set particles visibility
    for mut particle_visibility in set.p1().iter_mut() {
        update_visibility(&mut particle_visibility, setting.GRAPHICS_ENABLE_ALL_PARTICLES);
    }

    // Set stage parts visibility
    for mut visibility in set.p2().iter_mut() {
        update_visibility(&mut visibility, setting.GRAPHICS_ENABLE_STAGE);
    }

    // Set instrument visibility
    for (mut visibility, instrument_type, low_poly) in set.p3().iter_mut() {
        // info!("Updating visibility: {:?} | {:?} | {:?}",
        //     instrument_type.0,
        //     setting.GRAPHICS_USE_LOW_POLY_MODELS,
        //     low_poly
        // );

        match instrument_type.0 {
            // Pianos
            InstrumentType::PIANO_BENCH | InstrumentType::PIANO if setting.GRAPHICS_USE_LOW_POLY_MODELS => {
                on_low_poly_visibility(&mut visibility, low_poly.is_some(), setting.GRAPHICS_ENABLE_PIANO);
            }
            InstrumentType::PIANO_BENCH | InstrumentType::PIANO => {
                on_high_poly_visibility(&mut visibility, low_poly.is_none(), setting.GRAPHICS_ENABLE_PIANO);
            }

            // Drums
            InstrumentType::DRUMS if setting.GRAPHICS_USE_LOW_POLY_MODELS => {
                on_low_poly_visibility(&mut visibility, low_poly.is_some(), setting.GRAPHICS_ENABLE_DRUMS && drums_displayed.0);
            }
            InstrumentType::DRUMS => {
                on_high_poly_visibility(&mut visibility, low_poly.is_none(), setting.GRAPHICS_ENABLE_DRUMS && drums_displayed.0);
            }
            InstrumentType::GUITARS => {
                update_visibility(&mut visibility, setting.GRAPHICS_ENABLE_GUITARS);
            }
        }
    }

    // Camera settings
    for (view, mut camera, mut tone_mapping, mut msaa) in view_targets.iter_mut() {
        let mut camera_entity = commands.entity(view);
        camera.hdr = setting.GRAPHICS_ENABLE_HDR;

        // Msaa
        if setting.GRAPHICS_ENABLE_ANTIALIAS {
            *msaa = match setting.GRAPHICS_MSAA_SAMPLES {
                GraphicsMsaaSamples::Msaa_Sample2 => {
                    if cfg!(feature = "desktop") {
                        Msaa::Sample2
                    } else {
                        Msaa::Sample4
                    }
                },
                GraphicsMsaaSamples::Msaa_Sample4 => Msaa::Sample4,
                // Note: Sample8 is not supported in Web/WASM
                GraphicsMsaaSamples::Msaa_Sample8 => {
                    if cfg!(feature = "desktop") {
                        Msaa::Sample8
                    } else {
                        Msaa::Sample4
                    }
                }
                _ => Msaa::Off,
            };
        } else {
            camera_entity
                .remove::<bevy::core_pipeline::fxaa::Fxaa>()
                .remove::<bevy::core_pipeline::smaa::Smaa>();

            *msaa = Msaa::Off;
        }

        // Bloom
        if setting.GRAPHICS_ENABLE_BLOOM {
            camera_entity.insert(bevy::core_pipeline::bloom::Bloom::NATURAL);
        } else {
            camera_entity.remove::<bevy::core_pipeline::bloom::Bloom>();
        }

        // Shadow filter
        match setting.GRAPHICS_SHADOW_FILTER {
            GraphicShadowFilteringMethod::ShadowFiltering_Hardware2x2 => {
                camera_entity.insert(bevy::pbr::ShadowFilteringMethod::Hardware2x2);
            }
            GraphicShadowFilteringMethod::ShadowFiltering_Gaussian => {
                camera_entity.insert(bevy::pbr::ShadowFilteringMethod::Gaussian);
            }
            GraphicShadowFilteringMethod::ShadowFiltering_Temporal => {
                camera_entity.insert(bevy::pbr::ShadowFilteringMethod::Temporal);
            }
        };

        // Tonemapping
        if setting.GRAPHICS_ENABLE_TONE_MAPPING {
            *tone_mapping = Tonemapping::AgX;
        } else {
            *tone_mapping = Tonemapping::None;
        }

        // Fog
        if !setting.GRAPHICS_ENABLE_FOG {
            camera_entity.remove::<DistanceFog>();
        }

        // if setting.GRAPHICS_ENABLE_DEPTH_OF_FIELD {
        //     camera_entity.insert(DepthOfFieldSettings {
        //         mode: DepthOfFieldMode::Gaussian,
        //         focal_distance: 5.0,
        //         // aperture_f_stops: 1.0 / 8.0,
        //         // max_depth: 14.0,
        //         ..default()
        //     });
        //
        // } else {
        //     camera_entity.remove::<DepthOfFieldSettings>();
        // }
    }

    // Set drums display
    if !setting.GRAPHICS_ENABLE_DRUMS {
        commands.insert_resource(DrumsDisplayed(false));
    }
}

fn on_low_poly_visibility(mut visibility: &mut Visibility, is_low_poly: bool, set_visibility: bool) {
    update_visibility(&mut visibility, is_low_poly);

    // Apply logic for low poly model now
    if is_low_poly {
        update_visibility(&mut visibility, set_visibility);
    }
}

fn on_high_poly_visibility(mut visibility: &mut Visibility, is_high_poly: bool, set_visibility: bool) {
    update_visibility(&mut visibility, is_high_poly);

    // Apply logic for high poly model now
    if is_high_poly {
        update_visibility(&mut visibility, set_visibility);
    }
}

fn update_visibility<T>(item: &mut T, condition: bool)
where
    T: std::ops::DerefMut<Target = Visibility>,
{
    **item = if condition { Visibility::Inherited } else { Visibility::Hidden };
}

#[cfg(target_arch = "wasm32")]
pub fn broadcast_app_action(app_state_actions: AppStateActions) {
    let bytes = app_state_actions.write_to_bytes().unwrap_or_default();
    let js_output = &JsValue::from(js_sys::Uint8Array::from(&bytes[..]));
    let _result = crate::utils::emit_custom_event("renderer_actions", &js_output);
}

/// Broadcasts application state actions to external systems.
///
/// This function handles the serialization and transmission of `AppStateActions` messages:
/// - For WASM targets: Converts to bytes and emits a "renderer_actions" custom event
/// - For desktop targets: Sends through the unified event channel
///
/// # Arguments
///
/// * `app_state_actions` - The action message to broadcast
///
/// # Platform-specific Behavior
///
/// ## Web/WASM
/// Serializes the action to bytes and emits a "renderer_actions" custom DOM event
/// that can be handled by JavaScript code.
///
/// ## Desktop
/// Sends the action through a unified event channel to other parts of the application.
///
/// # Example
///
/// ```rust
/// let mut action = pianorhythm_proto::pianorhythm_actions::AppStateActions::new();
/// action.set_action(pianorhythm_proto::pianorhythm_actions::AppStateActions_Action::SynthAction);
/// pianorhythm_bevy_renderer::core::events::broadcast_app_action(action);
/// ```
#[cfg(not(target_arch = "wasm32"))]
#[cfg(feature = "desktop")]
pub fn broadcast_app_action(app_state_actions: AppStateActions) {
    unsafe {
        let Some(broadcast) = crate::utils::desktop_ffi::BROADCAST_APP_ACTION_EVENT_CHANNEL.get() else {
            return;
        };

        _ = broadcast.0.try_send(app_state_actions);
    }
}

pub fn broadcast_avatar_events(mut event_reader: EventReader<AvatarEventsBroadcastAction>) {
    for event in event_reader.read() {
        let mut action = AppStateActions::new();

        match event {
            AvatarEventsBroadcastAction::SetPosition { x, y, z } => {
                action.set_action(AppStateActions_Action::SetAvatarPosition);
                let mut position = AvatarWorldDataDto_AvatarMessageWorldPosition::new();
                position.set_x(*x as f64);
                position.set_y(*y as f64);
                position.set_z(*z as f64);
                action.set_avatarWorldPosition(position);
                broadcast_app_action(action);
            }
            _ => {}
        }
    }
}

/// Broadcasts synthesizer events to both the ECS system and external systems.
///
/// This function handles two types of synthesizer events:
/// - Note On events: When a note starts playing
/// - Note Off events: When a note stops playing
///
/// # Arguments
///
/// * `commands` - Bevy ECS commands for inserting resources
/// * `event_reader` - Reader for incoming synthesizer broadcast events
/// * `event_writer` - Writer for outgoing ECS synthesizer events
/// * `client_socket_id` - The current client's socket ID
///
/// # Events Flow
///
/// 1. Receives a `SynthEventsBroadcastAction` event
/// 2. Creates corresponding `AppStateActions` and `AudioSynthActions`
/// 3. For Note On events:
///    - Updates last mouse note if source is mouse
///    - Sends an ECS note on event
///    - Creates and broadcasts synth action with note data
/// 4. For Note Off events:
///    - Sends an ECS note off event
///    - Creates and broadcasts synth action with note data
pub fn broadcast_synth_events(
    mut commands: Commands, mut event_reader: EventReader<SynthEventsBroadcastAction>, mut event_writer: EventWriter<ECSSynthEventsAction>,
    client_socket_id: Res<ClientSocketIDStr>,
) {
    for event in event_reader.read() {
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SynthAction);
        let mut synth_action = AudioSynthActions::new();
        synth_action.set_sourceSocketID(client_socket_id.0.to_string());

        match event {
            SynthEventsBroadcastAction::NoteOn(data) => {
                if data.source == MidiNoteSource::MOUSE {
                    commands.insert_resource(LastMouseNoteOn(Some(data.note)));
                }

                event_writer.write(ECSSynthEventsAction::NoteOn(data.clone()));
                synth_action.set_action(AudioSynthActions_Action::NoteOn);

                let mut input = AudioSynthActionData::new();
                input.set_channel(data.channel.into());
                input.set_note(data.note.into());
                input.set_velocity(data.vel.into());
                input.set_noteSource(data.source.clone());
                synth_action.set_synthData(input);
                action.set_audioSynthAction(synth_action);

                broadcast_app_action(action);
            }
            SynthEventsBroadcastAction::NoteOff(data) => {
                event_writer.write(ECSSynthEventsAction::NoteOff(data.clone()));
                synth_action.set_action(AudioSynthActions_Action::NoteOff);

                let mut input = AudioSynthActionData::new();
                input.set_channel(data.channel.into());
                input.set_note(data.note.into());
                input.set_noteSource(data.source.clone());
                synth_action.set_synthData(input);
                action.set_audioSynthAction(synth_action);

                broadcast_app_action(action);
            }
        }
    }
}
