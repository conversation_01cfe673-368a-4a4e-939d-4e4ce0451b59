use bevy::ecs::system::SystemId;
use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use bevy_asset_loader::loading_state::{LoadingState, LoadingStateAppExt};
use bevy_asset_loader::prelude::ConfigureLoadingState;
use bevy_sequential_actions::*;
use protobuf::Message;
use smooth_bevy_cameras::LookTransform;

use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::room_renditions::RoomStages;

use crate::actions::animate_look_transform::AnimateCameraLookTransformAction;
use crate::components::{MainCamera, StagePart};
use crate::core::events::ECSWorldEvents;
use crate::core::AppState;
use crate::plugins::piano::resources::PianoModelAssets;
use crate::plugins::stages::resources::StageModelAssets;
use crate::resources::{DefaultCameraPosition, RoomStage};
use crate::types::StageState;
use crate::utils;

mod default_stage;
mod forest_stage;
mod resources;

#[derive(Debug, Clone, Copy, Default, Eq, PartialEq, Hash, States, Reflect)]
enum PluginState {
    #[default]
    AssetLoading,
    Ready,
}

#[derive(Default)]
pub struct StagesPlugin;

#[derive(Debug, Resource, Clone)]
struct CurrentStage(pub RoomStages);

#[derive(Resource)]
struct LoadStageSystems(HashMap<RoomStages, SystemId>);

impl FromWorld for LoadStageSystems {
    fn from_world(world: &mut World) -> Self {
        let mut systems = LoadStageSystems(HashMap::new());

        systems
            .0
            .insert(RoomStages::FOREST, world.register_system(forest_stage::load));

        systems
            .0
            .insert(RoomStages::THE_VOID, world.register_system(default_stage::load));

        systems
    }
}

impl Plugin for StagesPlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<StageState>()
            .init_resource::<LoadStageSystems>()
            .insert_resource(CurrentStage(RoomStages::UNKNOWN))
            .init_state::<PluginState>()
            .add_loading_state(
                LoadingState::new(PluginState::AssetLoading)
                    .continue_to_state(PluginState::Ready)
                    .with_dynamic_assets_file::<bevy_asset_loader::prelude::StandardDynamicAssetCollection>(
                        utils::get_model_assets_file_path(),
                    )
                    .load_collection::<StageModelAssets>(),
            )
            .add_systems(
                Update,
                check_room_stage_load
                    .run_if(resource_changed_or_removed::<RoomStage>.and(not(resource_added::<RoomStage>))),
            )
            .add_systems(
                OnTransition {
                    exited: StageState::Loaded,
                    entered: StageState::Loaded,
                },
                on_loaded_transition,
            )
            .add_systems(OnEnter(StageState::Loaded), on_loaded_transition)
            .add_systems(
                Update,
                on_room_stage_load
                    .run_if(state_changed::<StageState>)
                    .run_if(in_state(StageState::Loading)),
            );
    }
}

fn on_loaded_transition(
    mut commands: Commands,
    mut camera: Query<(Entity, &mut LookTransform), (With<MainCamera>, With<LookTransform>)>,
    default_camera_position: Res<DefaultCameraPosition>,
) {
    let Ok((entity, mut look_transform)) = camera.single_mut() else {
        return;
    };

    let target_start = Vec3::new(0., 0.5, 0.);
    look_transform.target = target_start;

    let eye_start = Vec3::new(-2.7, 3.98, 10.02);
    look_transform.eye = eye_start;

    commands.actions(entity).add(AnimateCameraLookTransformAction {
        target_end: default_camera_position.target,
        eye_end: Some(default_camera_position.eye),
        duration: Some(2000),
        delay: Some(2000),
        ..default()
    });
}

fn check_room_stage_load(
    stage_details: Res<RoomStage>,
    current_stage: Res<CurrentStage>,
    current_app_state: Res<State<AppState>>,
    mut next_state: ResMut<NextState<StageState>>,
    mut next_app_state: ResMut<NextState<AppState>>,
    mut event_writer: EventWriter<ECSWorldEvents>,
) {
    let load_room = stage_details.0.clone();
    let room_stage = load_room.get_roomStage();

    // ::log::info!("Loading room {:?} | {:?} | {:?}", room_stage, current_stage.0, current_app_state.get());

    // If the current stage is already the same, no need to reload it.
    // Immediately move to the next state.
    if current_stage.0 == room_stage && !current_app_state.get().eq(&AppState::AppLoading) {
        next_state.set(StageState::Loaded);
        next_app_state.set(AppState::InGame);
        event_writer.write(ECSWorldEvents::new(AppStateEvents::RoomStageLoaded));
        return;
    }

    next_state.set(StageState::Loading);
    next_app_state.set(AppState::RoomLoading);
}

fn on_room_stage_load(
    stage_details: Res<RoomStage>,
    current_state: Res<State<StageState>>,
    loaded_stage: Query<Entity, With<StagePart>>,
    systems: Res<LoadStageSystems>,
    mut event_writer: EventWriter<ECSWorldEvents>,
    mut commands: Commands,
    mut next_state: ResMut<NextState<StageState>>,
    mut next_app_state: ResMut<NextState<AppState>>,
) {
    if !current_state.get().eq(&StageState::Loading) {
        return;
    }

    let load_room = stage_details.0.clone();
    let room_stage = load_room.get_roomStage();
    commands.insert_resource(CurrentStage(room_stage.clone()));

    // Remove any existing stage parts
    for entity in loaded_stage.iter() {
        commands.entity(entity).despawn();
    }

    match room_stage {
        RoomStages::FOREST => commands.run_system(systems.0[&RoomStages::FOREST]),
        _ => commands.run_system(systems.0[&RoomStages::THE_VOID]),
    }

    next_state.set(StageState::Loaded);
    next_app_state.set(AppState::InGame);
    event_writer.write(ECSWorldEvents::new(AppStateEvents::RoomStageLoaded));

    #[cfg(debug_assertions)]
    log::info!(
        "Got room stage load!: {:?} | {:?} | {:?} | {:?}",
        load_room.get_roomStage(),
        load_room.get_roomType(),
        load_room.get_roomStageDetails(),
        load_room.is_initialized()
    );
}
