use protobuf::RepeatedField;
use std::cell::RefCell;
use std::rc::Rc;

use crate::common::note_buffer::adaptive_flush_timer::{AdaptiveFlushConfig, AdaptiveFlushTimer};
use pianorhythm_proto::midi_renditions::{MidiDto, MidiDtoType};
use pianorhythm_proto::server_message::{MidiMessageInputDto, MidiMessageInputDto_MidiMessageInputBuffer};

use crate::common::note_buffer::priority_buffer::{NotePriority, PriorityBuffer};
#[cfg(target_arch = "wasm32")]
use gloo_timers::callback::Timeout;
use crate::common::note_buffer::enhanced_time_sync::EnhancedTimeSync;

pub type NoteBufferEngineOnFlushedBuffer = Box<dyn Fn(MidiMessageInputDto) + Send + 'static>;

pub struct NoteBufferEngine {
    pub server_time_offset: i64,
    pub initialized: bool,
    pub debug_mode: bool,
    pub adaptive_flushing_enabled: bool,

    enhanced_time_sync: EnhancedTimeSync,
    note_buffer: Vec<MidiMessageInputDto_MidiMessageInputBuffer>,
    note_buffer_time: Option<i64>,
    adaptive_timer: AdaptiveFlushTimer,
    flush_callback: Option<Box<dyn Fn(u32) + Send + 'static>>, // Callback to reschedule flush
    priority_buffer: PriorityBuffer,
    max_note_buffer_size: usize,
    room_is_self_hosted: bool,
    client_is_self_muted: bool,
    stop_emitting_to_ws_when_alone: bool,
    on_handle: NoteBufferEngineOnFlushedBuffer,
    #[cfg(target_arch = "wasm32")]
    flush_timeout: Option<Timeout>,
}

impl NoteBufferEngine {
    pub fn new(on_handle: NoteBufferEngineOnFlushedBuffer) -> Self {
        NoteBufferEngine {
            note_buffer: vec![],
            note_buffer_time: None,
            server_time_offset: 0,
            max_note_buffer_size: 300,
            room_is_self_hosted: false,
            initialized: false,
            client_is_self_muted: false,
            debug_mode: false,
            stop_emitting_to_ws_when_alone: false,
            on_handle,
            adaptive_timer: AdaptiveFlushTimer::new(AdaptiveFlushConfig::default()),
            flush_callback: None,
            #[cfg(target_arch = "wasm32")]
            flush_timeout: None,
            adaptive_flushing_enabled: false,
            priority_buffer: PriorityBuffer::new(300),
            enhanced_time_sync: EnhancedTimeSync::new(),
        }
    }

    pub fn initialize(&self) {}

    pub fn dispose(&mut self) {
        self.set_adaptive_flushing_enabled(false);
        #[cfg(target_arch = "wasm32")]
        self.stop_adaptive_flushing();
        self.clean_up();
    }

    /// Resets the buffer and its start time.
    pub fn clean_up(&mut self) {
        self.note_buffer_time = None;
        self.note_buffer = vec![];
    }

    pub fn set_flush_callback(&mut self, callback: Box<dyn Fn(u32) + Send + 'static>) {
        self.flush_callback = Some(callback);
    }

    /// Starts the adaptive flushing system with internal timer management
    #[cfg(target_arch = "wasm32")]
    pub fn start_adaptive_flushing(&mut self) {
        if self.adaptive_flushing_enabled {
            return;
        }

        log::debug!("Starting adaptive flushing");
        self.adaptive_flushing_enabled = true;
        self.schedule_next_flush();
    }

    /// Stops the adaptive flushing system
    #[cfg(target_arch = "wasm32")]
    pub fn stop_adaptive_flushing(&mut self) {
        self.adaptive_flushing_enabled = false;
        if let Some(timeout) = self.flush_timeout.take() {
            timeout.cancel();
        }
    }

    /// Schedules the next flush based on adaptive timing
    #[cfg(target_arch = "wasm32")]
    pub fn schedule_next_flush(&mut self) {
        if !self.adaptive_flushing_enabled {
            return;
        }

        // Cancel existing timeout
        if let Some(timeout) = self.flush_timeout.take() {
            timeout.cancel();
        }

        // Calculate next interval
        let buffer_utilization = self.note_buffer.len() as f32 / self.max_note_buffer_size as f32;
        let next_interval = self.adaptive_timer.calculate_next_interval(buffer_utilization);

        if next_interval == 0 {
            // Flush immediately and schedule next
            self.flush_buffer();
            self.schedule_next_flush();
        } else {
            // We'll need to handle the scheduling differently since we can't capture self
            // For now, let's use the existing callback mechanism if available
            if let Some(callback) = &self.flush_callback {
                callback(next_interval);
            }
        }
    }

    /// Gets the current recommended flush interval
    pub fn get_current_flush_interval(&mut self) -> u32 {
        let buffer_utilization = self.note_buffer.len() as f32 / self.max_note_buffer_size as f32;
        self.adaptive_timer.calculate_next_interval(buffer_utilization)
    }

    /// Checks if buffer should be flushed immediately
    pub fn should_flush_immediately(&self) -> bool {
        let buffer_utilization = self.note_buffer.len() as f32 / self.max_note_buffer_size as f32;
        self.adaptive_timer.should_flush_immediately(buffer_utilization)
    }

    /// Sets the adaptive flushing enabled state
    pub fn set_adaptive_flushing_enabled(&mut self, enabled: bool) {
        log::debug!("set_adaptive_flushing_enabled: {}", enabled);
        self.adaptive_flushing_enabled = enabled;
    }

    /// Gets the adaptive flushing enabled state
    pub fn is_adaptive_flushing_enabled(&self) -> bool {
        self.adaptive_flushing_enabled
    }

    /// Flushes the current buffer of notes via the on_handle callback.
    pub fn flush_buffer(&mut self) {
        if let Some(nbft) = self.note_buffer_time {
            if self.priority_buffer.is_empty() {
                return;
            }

            let mut output = MidiMessageInputDto::new();
            output.set_time(format!("{}", nbft + self.server_time_offset));

            let notes = self.priority_buffer.flush_all();
            //let buffer_to_send = std::mem::take(&mut self.note_buffer);
            output.set_data(RepeatedField::from_vec(notes));

            if self.debug_mode {
                // log::info!("Note Buffer Output {:?} | {}", &output, self.server_time_offset);
            }

            (self.on_handle)(output);

            self.note_buffer_time = None;
            self.note_buffer = vec![];
        }
    }

    /// Toggles whether to process messages when the user is alone.
    /// Clears the buffer if set to true.
    pub fn stop_emitting_to_ws_when_alone(&mut self, is_alone: bool) {
        self.stop_emitting_to_ws_when_alone = is_alone;

        if is_alone {
            self.clean_up();
        }
    }

    /// Sets if the room is hosted by the current user. If so, MIDI messages
    /// from this client are typically ignored to prevent echo.
    pub fn set_room_is_self_hosted(&mut self, value: bool) {
        self.room_is_self_hosted = value;
    }

    /// Toggles the client's mute status.
    /// Clears the buffer if muted.
    pub fn set_client_is_muted(&mut self, is_muted: bool) {
        self.client_is_self_muted = is_muted;

        if is_muted {
            self.clean_up();
        }
    }

    /// Processes an incoming MIDI message, adding it to the buffer if conditions are met.
    pub fn process_message(&mut self, dto: MidiDto) {
        if self.client_is_self_muted || self.room_is_self_hosted || self.stop_emitting_to_ws_when_alone {
            return;
        }

        // Record note activity for adaptive timing
        self.adaptive_timer.record_note_activity();

        let local_time = chrono::Utc::now().timestamp_millis();
        
        match self.note_buffer_time {
            None => {
                self.note_buffer_time = Some(self.get_server_time(local_time));
                let mut delay = 0;

                // Minimum delay
                if dto.messageType == MidiDtoType::NoteOff {
                    delay = 40;
                }

                self.push_to_note_buffer(dto, delay);
            }
            Some(nbft) => {
                let current_server_time = self.get_server_time(local_time);
                let delay = current_server_time - nbft;
                self.push_to_note_buffer(dto, delay);
            }
        }

        // Check if we should flush immediately or reschedule
        let buffer_utilization = self.note_buffer.len() as f32 / self.max_note_buffer_size as f32;
        let next_interval = self.adaptive_timer.calculate_next_interval(buffer_utilization);

        if next_interval == 0 {
            // Flush immediately
            self.flush_buffer();
        } else if let Some(callback) = &self.flush_callback {
            // Reschedule flush with new interval
            callback(next_interval);
        }
    }

    fn push_to_note_buffer(&mut self, dto: MidiDto, delay: i64) {
        let priority = NotePriority::from_midi_dto(&dto);

        let mut buffer = MidiMessageInputDto_MidiMessageInputBuffer::new();
        buffer.set_delay(delay as f64);
        buffer.set_data(dto);

        let added = self.priority_buffer.add_note(buffer, priority);

        if !added && self.debug_mode {
            log::warn!("Failed to add note to priority buffer - buffer may be full");
        }
    }
}

impl NoteBufferEngine {
    /// Updates time synchronization with server timing data
    pub fn update_server_timing(&mut self, ping_time: i64, server_time: i64, local_time: i64) {
        self.enhanced_time_sync.update_timing(ping_time, server_time, local_time);

        // Update the legacy server_time_offset for backward compatibility
        self.server_time_offset = self.enhanced_time_sync.get_synchronized_time(local_time) - local_time;

        if self.debug_mode {
            log::info!(
                "Time sync updated - Confidence: {:.2}, Offset: {}ms",
                self.enhanced_time_sync.get_confidence(),
                self.server_time_offset
            );
        }
    }

    /// Gets the current synchronized server time
    pub fn get_server_time(&self, local_time: i64) -> i64 {
        self.enhanced_time_sync.get_synchronized_time(local_time)
    }

    /// Gets the confidence level of time synchronization
    pub fn get_sync_confidence(&self) -> f64 {
        self.enhanced_time_sync.get_confidence()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use pianorhythm_proto::midi_renditions::{MidiDto_MidiNoteOff, MidiDto_MidiNoteOn};
    use std::sync::{Arc, Mutex};
    use std::thread;
    use std::time::Duration;

    // Helper to create a NoteOn MidiDto for testing.
    fn create_note_on_dto(note: i32, velocity: i32) -> MidiDto {
        let mut midi_dto = MidiDto::new();
        midi_dto.set_messageType(MidiDtoType::NoteOn);
        let mut note_on = MidiDto_MidiNoteOn::new();
        note_on.set_channel(0);
        note_on.set_note(note);
        note_on.set_velocity(velocity);
        midi_dto.set_noteOn(note_on);
        midi_dto
    }

    // Helper to create a NoteOff MidiDto for testing.
    fn create_note_off_dto(note: i32) -> MidiDto {
        let mut midi_dto = MidiDto::new();
        midi_dto.set_messageType(MidiDtoType::NoteOff);
        let mut note_off = MidiDto_MidiNoteOff::new();
        note_off.set_channel(0);
        note_off.set_note(note);
        midi_dto.set_noteOff(note_off);
        midi_dto
    }

    fn create_test_note(note: i32, priority: NotePriority) -> MidiMessageInputDto_MidiMessageInputBuffer {
        let mut buffer = MidiMessageInputDto_MidiMessageInputBuffer::new();
        buffer.set_delay(0.0);
        buffer.set_data(create_note_on_dto(note, 100));
        buffer
    }

    #[test]
    fn test_process_and_flush_happy_path() {
        // Use Arc<Mutex<...>> to capture the flushed data from the callback in a thread-safe way.
        let flushed_data = Arc::new(Mutex::new(None));
        let flushed_data_clone = Arc::clone(&flushed_data);

        let on_handle = Box::new(move |dto: MidiMessageInputDto| {
            let mut data = flushed_data_clone.lock().unwrap();
            *data = Some(dto);
        });

        let mut engine = NoteBufferEngine::new(on_handle);
        engine.server_time_offset = 1000; // Set a known offset for predictable time calculations.

        // Process two messages
        let note_on_1 = create_note_on_dto(60, 100);
        engine.process_message(note_on_1.clone());
        assert_eq!(engine.note_buffer.len(), 1);
        assert!(engine.note_buffer_time.is_some());

        // Introduce a small delay to test the delay calculation.
        thread::sleep(Duration::from_millis(50));

        let note_on_2 = create_note_on_dto(62, 110);
        engine.process_message(note_on_2.clone());
        assert_eq!(engine.note_buffer.len(), 2);

        // Flush the buffer
        let buffer_time_before_flush = engine.note_buffer_time.unwrap();
        engine.flush_buffer();

        // Assert buffer is cleared after flush
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());

        // Assert that the on_handle callback was called with the correct data
        let locked_data = flushed_data.lock().unwrap();
        let flushed_dto = locked_data.as_ref().expect("on_handle was not called");

        assert_eq!(flushed_dto.get_data().len(), 2);
        assert_eq!(flushed_dto.get_time(), format!("{}", buffer_time_before_flush + engine.server_time_offset));

        // Check first message in buffer
        let msg1 = &flushed_dto.get_data()[0];
        assert_eq!(msg1.get_delay(), 0.0); // First message has 0 delay.
        assert_eq!(msg1.get_data(), &note_on_1);

        // Check second message in buffer
        let msg2 = &flushed_dto.get_data()[1];
        assert!(msg2.get_delay() >= 50.0); // Delay should be at least 50ms.
        assert!(msg2.get_delay() < 100.0); // And reasonably less than a much larger value.
        assert_eq!(msg2.get_data(), &note_on_2);
    }

    #[test]
    fn test_flush_empty_buffer_does_nothing() {
        // Use a flag to check if the handler was called.
        let handler_called = Arc::new(Mutex::new(false));
        let handler_called_clone = Arc::clone(&handler_called);

        let on_handle = Box::new(move |_| {
            *handler_called_clone.lock().unwrap() = true;
        });

        let mut engine = NoteBufferEngine::new(on_handle);
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());

        engine.flush_buffer();

        // The handler should NOT have been called.
        assert_eq!(*handler_called.lock().unwrap(), false);
    }

    #[test]
    fn test_messages_are_ignored_when_muted() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.set_client_is_muted(true);
        assert!(engine.client_is_self_muted);

        engine.process_message(create_note_on_dto(60, 100));

        assert!(engine.note_buffer.is_empty());
    }

    #[test]
    fn test_setting_muted_clears_buffer() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.process_message(create_note_on_dto(60, 100));
        assert_eq!(engine.note_buffer.len(), 1);

        // Muting should clear any pending notes.
        engine.set_client_is_muted(true);
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());
    }

    #[test]
    fn test_messages_are_ignored_when_self_hosted() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.set_room_is_self_hosted(true);
        assert!(engine.room_is_self_hosted);

        engine.process_message(create_note_on_dto(60, 100));

        assert!(engine.note_buffer.is_empty());
    }

    #[test]
    fn test_messages_are_ignored_when_alone() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.stop_emitting_to_ws_when_alone(true);
        assert!(engine.stop_emitting_to_ws_when_alone);

        engine.process_message(create_note_on_dto(60, 100));

        assert!(engine.note_buffer.is_empty());
    }

    #[test]
    fn test_setting_alone_clears_buffer() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.process_message(create_note_on_dto(60, 100));
        assert_eq!(engine.note_buffer.len(), 1);

        // Setting alone status should clear pending notes.
        engine.stop_emitting_to_ws_when_alone(true);
        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());
    }

    #[test]
    fn test_note_buffer_max_size() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.max_note_buffer_size = 5;

        for i in 0..10 {
            engine.process_message(create_note_on_dto(60 + i, 100));
        }

        // The buffer should not grow past the max size.
        assert_eq!(engine.note_buffer.len(), 5);
    }

    #[test]
    fn test_initial_note_off_delay() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));

        // The first message is a NoteOff.
        engine.process_message(create_note_off_dto(60));

        assert_eq!(engine.note_buffer.len(), 1);
        let buffered_message = &engine.note_buffer[0];

        // It should have the special hardcoded delay.
        assert_eq!(buffered_message.get_delay(), 40.0);
    }

    #[test]
    fn test_dispose_clears_buffer() {
        let mut engine = NoteBufferEngine::new(Box::new(|_| {}));
        engine.process_message(create_note_on_dto(60, 100));
        assert_eq!(engine.note_buffer.len(), 1);
        assert!(engine.note_buffer_time.is_some());

        engine.dispose();

        assert!(engine.note_buffer.is_empty());
        assert!(engine.note_buffer_time.is_none());
    }

    #[test]
    fn test_adaptive_flush_timing() {
        let mut timer = AdaptiveFlushTimer::new(AdaptiveFlushConfig::default());

        // Test high activity scenario
        for _ in 0..10 {
            timer.record_note_activity();
            std::thread::sleep(Duration::from_millis(10));
        }

        let interval = timer.calculate_next_interval(0.5);
        assert!(interval <= 50, "High activity should result in short interval");

        // Test low activity scenario
        std::thread::sleep(Duration::from_millis(500));
        let interval = timer.calculate_next_interval(0.1);
        assert!(interval >= 100, "Low activity should result in longer interval");
    }

    #[test]
    fn test_priority_buffer_overflow() {
        let mut buffer = PriorityBuffer::new(10);

        // Fill buffer with normal notes
        for i in 0..15 {
            let note = create_test_note(i, NotePriority::Normal);
            buffer.add_note(note, NotePriority::Normal);
        }

        // Add critical note - should always succeed
        let critical_note = create_test_note(100, NotePriority::Critical);
        let added = buffer.add_note(critical_note, NotePriority::Critical);
        assert!(added, "Critical notes should always be added");

        // Verify critical note is in buffer
        let flushed = buffer.flush_all();
        assert!(flushed.iter().any(|n| n.get_data().has_noteOn() &&
            n.get_data().get_noteOn().get_note() == 100));
    }
}
