use pianorhythm_proto::midi_renditions::{MidiDto, MidiDtoType};
use pianorhythm_proto::server_message::MidiMessageInputDto_MidiMessageInputBuffer;
use std::collections::VecDeque;

/// Represents the priority of a MIDI note event.
/// 
/// - `Critical`: Note on/off events (highest priority)
/// - `Important`: Control changes, program changes
/// - `Normal`: Other MIDI events (lowest priority)
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum NotePriority {
    /// Note on/off events
    Critical = 3,
    /// Control changes, program changes
    Important = 2,
    /// Other MIDI events
    Normal = 1,
}

impl NotePriority {
    /// Determines the priority of a MIDI DTO message.
    ///
    /// # Arguments
    ///
    /// * `dto` - Reference to a `MidiDto` message.
    ///
    /// # Returns
    ///
    /// * `NotePriority` corresponding to the message type.
    pub fn from_midi_dto(dto: &MidiDto) -> Self {
        match dto.messageType {
            MidiDtoType::NoteOn | MidiDtoType::NoteOff => NotePriority::Critical,
            MidiDtoType::Sustain | MidiDtoType::AllSoundOff => NotePriority::Important,
            _ => NotePriority::Normal,
        }
    }
}

/// A buffer that stores MIDI messages with prioritization.
/// 
/// Maintains separate queues for critical, important, and normal notes.
/// Ensures higher priority notes are retained when the buffer is full.
pub struct PriorityBuffer {
    critical_notes: VecDeque<MidiMessageInputDto_MidiMessageInputBuffer>,
    important_notes: VecDeque<MidiMessageInputDto_MidiMessageInputBuffer>,
    normal_notes: VecDeque<MidiMessageInputDto_MidiMessageInputBuffer>,
    max_size_per_priority: usize,
    total_capacity: usize,
}

impl PriorityBuffer {
    /// Creates a new `PriorityBuffer` with the specified total capacity.
    ///
    /// The capacity is divided equally among the three priority levels.
    ///
    /// # Arguments
    ///
    /// * `total_capacity` - The total number of notes the buffer can hold.
    ///
    /// # Returns
    ///
    /// * A new `PriorityBuffer` instance.
    pub fn new(total_capacity: usize) -> Self {
        let size_per_priority = total_capacity / 3;
        Self {
            critical_notes: VecDeque::with_capacity(size_per_priority),
            important_notes: VecDeque::with_capacity(size_per_priority),
            normal_notes: VecDeque::with_capacity(size_per_priority),
            max_size_per_priority: size_per_priority,
            total_capacity,
        }
    }

    /// Adds a note to the buffer with the given priority.
    ///
    /// - Critical notes may evict the oldest critical note if full.
    /// - Important notes may evict normal notes first, then oldest important note.
    /// - Normal notes are only added if there is space, evicting oldest normal note if needed.
    ///
    /// # Arguments
    ///
    /// * `note` - The MIDI message to add.
    /// * `priority` - The priority of the note.
    ///
    /// # Returns
    ///
    /// * `true` if the note was added, `false` otherwise.
    pub fn add_note(&mut self, note: MidiMessageInputDto_MidiMessageInputBuffer, priority: NotePriority) -> bool {
        match priority {
            NotePriority::Critical => {
                if self.critical_notes.len() >= self.max_size_per_priority {
                    // Make room by dropping oldest critical note
                    self.critical_notes.pop_front();
                }
                self.critical_notes.push_back(note);
                true
            }
            NotePriority::Important => {
                if self.important_notes.len() >= self.max_size_per_priority {
                    // Try to make room by dropping normal notes first
                    if !self.normal_notes.is_empty() {
                        self.normal_notes.pop_front();
                    } else {
                        self.important_notes.pop_front();
                    }
                }
                self.important_notes.push_back(note);
                true
            }
            NotePriority::Normal => {
                if self.get_total_size() >= self.total_capacity {
                    // Drop normal notes when buffer is full
                    if !self.normal_notes.is_empty() {
                        self.normal_notes.pop_front();
                    } else {
                        return false; // Can't add normal note when buffer is full of higher priority
                    }
                }
                self.normal_notes.push_back(note);
                true
            }
        }
    }

    /// Removes and returns all notes from the buffer in priority order.
    ///
    /// # Returns
    ///
    /// * `Vec<MidiMessageInputDto_MidiMessageInputBuffer>` containing all notes, highest priority first.
    pub fn flush_all(&mut self) -> Vec<MidiMessageInputDto_MidiMessageInputBuffer> {
        let mut result = Vec::new();

        // Flush in priority order
        result.extend(self.critical_notes.drain(..));
        result.extend(self.important_notes.drain(..));
        result.extend(self.normal_notes.drain(..));

        result
    }

    /// Returns the total number of notes currently in the buffer.
    pub fn get_total_size(&self) -> usize {
        self.critical_notes.len() + self.important_notes.len() + self.normal_notes.len()
    }

    /// Returns the buffer utilization as a float between 0.0 and 1.0.
    pub fn get_utilization(&self) -> f32 {
        self.get_total_size() as f32 / self.total_capacity as f32
    }

    /// Returns `true` if the buffer is empty.
    pub fn is_empty(&self) -> bool {
        self.get_total_size() == 0
    }
}