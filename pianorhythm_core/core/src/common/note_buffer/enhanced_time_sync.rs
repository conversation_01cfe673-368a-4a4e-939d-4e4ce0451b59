use std::collections::VecDeque;

/// `EnhancedTimeSync` provides advanced time synchronization between a client and server,
/// accounting for network latency, jitter, and confidence in the synchronization.
/// It maintains a buffer of recent time offsets to calculate a stable server offset.
pub struct EnhancedTimeSync {
    /// The current estimated offset between server and local time.
    server_offset: i64,
    /// The estimated one-way network latency.
    network_latency: i64,
    /// Buffer of recent time offset samples to smooth out jitter.
    jitter_buffer: VecDeque<i64>,
    /// Confidence level (0.0 to 1.0) in the current time synchronization.
    confidence_level: f64,
    /// Maximum number of jitter samples to keep in the buffer.
    max_jitter_samples: usize,
    /// The local time at which the last synchronization occurred.
    last_sync_time: Option<i64>,
}

impl EnhancedTimeSync {
    /// Creates a new `EnhancedTimeSync` instance with default parameters.
    pub fn new() -> Self {
        Self {
            server_offset: 0,
            network_latency: 0,
            jitter_buffer: VecDeque::with_capacity(10),
            confidence_level: 0.0,
            max_jitter_samples: 10,
            last_sync_time: None,
        }
    }

    /// Updates the time synchronization state with a new timing sample.
    ///
    /// # Arguments
    ///
    /// * `ping_time` - The round-trip time (RTT) in milliseconds.
    /// * `server_time` - The server's reported time in milliseconds.
    /// * `local_time` - The local time in milliseconds when the sample was taken.
    pub fn update_timing(&mut self, ping_time: i64, server_time: i64, local_time: i64) {
        let rtt = ping_time;
        let estimated_server_time = server_time + (rtt / 2);
        let new_offset = estimated_server_time - local_time;

        // Update jitter buffer
        if self.jitter_buffer.len() >= self.max_jitter_samples {
            self.jitter_buffer.pop_front();
        }
        self.jitter_buffer.push_back(new_offset);

        // Calculate stable offset using median
        let mut sorted_offsets: Vec<i64> = self.jitter_buffer.iter().cloned().collect();
        sorted_offsets.sort();

        if !sorted_offsets.is_empty() {
            self.server_offset = sorted_offsets[sorted_offsets.len() / 2];
            self.network_latency = rtt / 2;
            self.confidence_level = self.calculate_confidence(&sorted_offsets);
            self.last_sync_time = Some(local_time);
        }
    }

    /// Returns the synchronized server time based on the local time and current offset.
    /// If the confidence level is too low, returns the local time and logs a warning.
    ///
    /// # Arguments
    ///
    /// * `local_time` - The current local time in milliseconds.
    ///
    /// # Returns
    ///
    /// The synchronized server time in milliseconds.
    pub fn get_synchronized_time(&self, local_time: i64) -> i64 {
        if self.confidence_level > 0.7 {
            local_time + self.server_offset
        } else {
            log::warn!("Low time sync confidence: {:.2}", self.confidence_level);
            local_time
        }
    }

    /// Returns the current confidence level in the time synchronization.
    ///
    /// # Returns
    ///
    /// A value between 0.0 (no confidence) and 1.0 (high confidence).
    pub fn get_confidence(&self) -> f64 {
        self.confidence_level
    }

    /// Calculates the confidence level based on the standard deviation of the offset samples.
    ///
    /// # Arguments
    ///
    /// * `offsets` - A slice of recent offset samples.
    ///
    /// # Returns
    ///
    /// A confidence value between 0.0 and 1.0.
    fn calculate_confidence(&self, offsets: &[i64]) -> f64 {
        if offsets.len() < 3 {
            return 0.0;
        }

        let mean = offsets.iter().sum::<i64>() as f64 / offsets.len() as f64;
        let variance = offsets.iter()
            .map(|&x| (x as f64 - mean).powi(2))
            .sum::<f64>() / offsets.len() as f64;
        let std_dev = variance.sqrt();

        // Higher confidence for lower standard deviation
        (100.0 / (1.0 + std_dev)).min(1.0)
    }
}