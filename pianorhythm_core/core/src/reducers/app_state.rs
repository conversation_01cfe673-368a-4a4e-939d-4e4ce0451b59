use std::ops::Deref;
use std::rc::Rc;

use protobuf::{CodedInputStream, Message};
use reactive_state::{Reducer, ReducerResult};
use serde::Serialize;

use pianorhythm_proto::pianorhythm_actions::*;
use pianorhythm_proto::pianorhythm_app_renditions::{AppCommonEnvironment, AppSettings, GraphicShadowFilteringMethod, GraphicsMsaaSamples, GraphicsPresets};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::*;

use crate::reducers::audio_process_state::AudioProcessState;
use crate::reducers::client_state::ClientState;
use crate::reducers::current_room_state::CurrentRoomState;
use crate::reducers::rooms_state::RoomsState;

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Debu<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
/// Represents the state of the application.
pub struct AppState {
    /// Indicates whether all UI elements are disabled.
    pub disable_all_ui: bool,
    /// Indicates whether the canvas is loaded.
    pub canvas_loaded: bool,
    /// Indicates whether the user is logged in.
    pub logged_in: bool,
    /// Indicates whether the application is running on a mobile device.
    pub is_mobile: bool,
    /// Indicates whether the application is offline.
    pub is_offline: bool,
    /// Indicates whether the application has been initialized.
    pub initialized: bool,
    /// Indicates whether the application is in debug mode.
    pub is_debug_mode: bool,
    /// Indicates whether the graphics engine is disabled.
    pub graphics_engine_disabled: bool,
    /// Represents the state of the client.
    pub client_state: ClientState,
    /// Represents the current state of the room.
    pub current_room_state: CurrentRoomState,
    /// Represents the state of the rooms list.
    pub rooms_list_state: RoomsState,
    /// Represents the state of audio processing.
    pub audio_process_state: AudioProcessState,
    /// Indicates whether to stop emitting data to WebSocket when alone.
    pub stop_emitting_to_ws_when_alone: bool,
    #[serde(serialize_with = "pianorhythm_shared::util::proto_serialize")]
    /// Represents the application settings.
    pub app_settings: AppSettings,
    #[serde(serialize_with = "pianorhythm_shared::util::proto_serialize")]
    /// Represents the application settings.
    pub app_environment: AppCommonEnvironment,
}

impl AppState {
    pub fn new() -> Self {
        Self::default()
    }
}

#[derive(Clone, Default)]
pub struct AppStateReducer;

impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for AppStateReducer {
    fn reduce(&self, prev_state: &Rc<AppState>, action: &AppStateActions) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
        let mut events: Vec<AppStateEvents> = Vec::new();
        let mut effects: Vec<AppStateEffects> = Vec::new();

        let current_state = &prev_state.deref().clone();
        let mut new_state = (**prev_state).clone();

        let bool_value = action.get_boolValue();

        match action.action {
            AppStateActions_Action::InitializeAppState if !prev_state.initialized => {
                log::trace!("App state initialized");
                new_state.initialized = true;
                new_state.app_settings.GRAPHICS_PRESET = GraphicsPresets::Preset_None;
            }
            AppStateActions_Action::TriggerOfflineMode if new_state.is_offline != action.get_boolValue() => {
                log::info!("Triggering offline mode");
                new_state.is_offline = action.get_boolValue();

                if new_state.is_offline {
                    events.push(AppStateEvents::OfflineModeEnabled);
                } else {
                    events.push(AppStateEvents::OfflineModeDisabled);
                }
            }
            AppStateActions_Action::ResetState => {
                new_state = AppState::default();
                new_state.app_settings.GRAPHICS_PRESET = GraphicsPresets::Preset_None;
                events.push(AppStateEvents::AppStateReset);
            }
            AppStateActions_Action::DisableUI if !prev_state.disable_all_ui => {
                new_state.disable_all_ui = true;
                events.push(AppStateEvents::UIDisabled);
            }
            AppStateActions_Action::EnableUI if prev_state.disable_all_ui => {
                new_state.disable_all_ui = false;
            }
            AppStateActions_Action::SetIsMobile if action.has_boolValue() => {
                new_state.is_mobile = action.get_boolValue();
            }
            AppStateActions_Action::SetCanvasLoaded if !prev_state.canvas_loaded => {
                new_state.canvas_loaded = true;
                events.push(AppStateEvents::CanvasLoaded);
            }
            AppStateActions_Action::SetLoggedIn if prev_state.logged_in != bool_value => {
                new_state.logged_in = bool_value;
                if bool_value {
                    events.push(AppStateEvents::ClientLoggedIn);
                } else {
                    events.push(AppStateEvents::ClientLoggedOut);
                }
            }
            AppStateActions_Action::SetCommonEnvironment if action.has_appCommonEnvironment() && !action.get_appCommonEnvironment().eq(&prev_state.app_environment) => {
                new_state.app_environment = action.get_appCommonEnvironment().clone();
            }
            AppStateActions_Action::SetAppSettings if action.has_appSettings() && !action.get_appSettings().eq(&prev_state.app_settings) => {
                let mut new_app_settings = action.get_appSettings().clone();
                new_state.is_debug_mode = new_app_settings.ENABLE_DEBUG_MODE;

                // Audio settings
                new_state.audio_process_state.is_drum_channel_muted = !new_app_settings.get_AUDIO_ENABLE_DRUM_CHANNEL();
                new_state.audio_process_state.listen_to_program_changes = new_app_settings.get_MIDI_LISTEN_TO_PROGRAM_CHANGES();
                new_state.audio_process_state.reverb_enabled = new_app_settings.get_AUDIO_ENABLE_REVERB();
                new_state.audio_process_state.max_multi_mode_channels = new_app_settings.get_AUDIO_MULTIMODE_MAX_CHANNELS() as u8;
                new_state.audio_process_state.equalizer_enabled = new_app_settings.get_AUDIO_ENABLE_EQUALIZER();
                new_state.audio_process_state.slot_mode = new_app_settings.get_SLOT_MODE();

                // Graphics Settings
                #[cfg(debug_assertions)]
                log::debug!("Current: {:?} | Incoming: {:?}", new_state.app_settings.GRAPHICS_PRESET, action.get_appSettings().GRAPHICS_PRESET);

                let target_preset = action.get_appSettings().GRAPHICS_PRESET;

                // Set to default app settings
                if target_preset == GraphicsPresets::Preset_None {
                    new_app_settings.set_GRAPHICS_PRESET(GraphicsPresets::Preset_High);
                }

                // Update certain settings based on graphics preset
                if !new_state.app_settings.GRAPHICS_PRESET.eq(&new_app_settings.GRAPHICS_PRESET) {
                    match new_app_settings.GRAPHICS_PRESET {
                        GraphicsPresets::Preset_Low => {
                            new_app_settings = on_preset_low_graphics(&new_app_settings);
                        }
                        GraphicsPresets::Preset_Medium => {
                            new_app_settings = on_preset_medium_graphics(&new_app_settings);
                        }
                        GraphicsPresets::Preset_High => {
                            new_app_settings = on_preset_high_graphics(&new_app_settings);
                        }
                        GraphicsPresets::Preset_Ultra => {
                            new_app_settings = on_preset_ultra_graphics(&new_app_settings);
                        }
                        _ => {}
                    };
                }

                // After potentially applying a full preset, or if only individual settings were changed,
                // determine if the current configuration still matches a known preset.
                let mut is_custom = false;
                // Create a pure version of the settings based on the current preset value for comparison.
                let pure_preset_settings = match new_app_settings.GRAPHICS_PRESET {
                    GraphicsPresets::Preset_Low => Some(on_preset_low_graphics(&new_app_settings)),
                    GraphicsPresets::Preset_Medium => Some(on_preset_medium_graphics(&new_app_settings)),
                    GraphicsPresets::Preset_High => Some(on_preset_high_graphics(&new_app_settings)),
                    GraphicsPresets::Preset_Ultra => Some(on_preset_ultra_graphics(&new_app_settings)),
                    _ => None,
                };

                // If the current settings don't match the pure preset, they are custom.
                if let Some(pure_settings) = pure_preset_settings {
                    if !new_app_settings.eq(&pure_settings) {
                        is_custom = true;
                    }
                }

                if is_custom {
                    new_app_settings.set_GRAPHICS_PRESET(GraphicsPresets::Preset_Custom);
                }
                
                new_state.app_settings = new_app_settings;
            }
            _ => {}
        }

        if !current_state.app_settings.eq(&new_state.app_settings) {
            effects.push(pianorhythm_shared::util::create_effect_with(
                AppStateEffects_Action::AppSettingsUpdated, |effect|
                effect.set_appSettings(new_state.app_settings.clone()),
            ));
        }

        ReducerResult {
            state: Rc::new(new_state),
            events,
            effects,
        }
    }
}

fn on_preset_low_graphics(input: &AppSettings) -> AppSettings {
    let mut app_settings = input.clone();

    app_settings.GRAPHICS_USE_LOW_POLY_MODELS = true;
    app_settings.GRAPHICS_ENABLE_MOTION_BLUR = false;
    app_settings.GRAPHICS_ENABLE_ALL_PARTICLES = false;
    app_settings.GRAPHICS_ENABLE_FOG = false;
    app_settings.GRAPHICS_ENABLE_GLOW = false;
    app_settings.GRAPHICS_ENABLE_AVATARS = false;
    app_settings.GRAPHICS_ENABLE_ANTIALIAS = false;
    app_settings.GRAPHICS_ENABLE_SHADOWS = false;
    app_settings.GRAPHICS_ENABLE_PHYSICS = false;
    app_settings.GRAPHICS_ENABLE_AMBIENT_OCCLUSION = false;
    app_settings.GRAPHICS_ENABLE_DEPTH_OF_FIELD = false;
    app_settings.GRAPHICS_ENABLE_BLOOM = false;
    app_settings.GRAPHICS_ENABLE_TONE_MAPPING = false;
    app_settings.GRAPHICS_ENABLE_HDR = false;
    app_settings.GRAPHICS_MSAA_SAMPLES = GraphicsMsaaSamples::Msaa_Off;
    app_settings.GRAPHICS_SHADOW_FILTER = GraphicShadowFilteringMethod::ShadowFiltering_Hardware2x2;
    app_settings.GRAPHICS_ENABLE_ANIMATIONS = true;
    app_settings.GRAPHICS_ENABLE_LIGHTS = true;
    app_settings
}

fn on_preset_medium_graphics(input: &AppSettings) -> AppSettings {
    let mut app_settings = input.clone();

    app_settings.GRAPHICS_USE_LOW_POLY_MODELS = false;
    app_settings.GRAPHICS_ENABLE_MOTION_BLUR = false;
    app_settings.GRAPHICS_ENABLE_ALL_PARTICLES = false;
    app_settings.GRAPHICS_ENABLE_FOG = false;
    app_settings.GRAPHICS_ENABLE_GLOW = false;
    app_settings.GRAPHICS_ENABLE_AVATARS = true;
    app_settings.GRAPHICS_ENABLE_ANTIALIAS = true;
    app_settings.GRAPHICS_ENABLE_SHADOWS = true;
    app_settings.GRAPHICS_ENABLE_PHYSICS = false;
    app_settings.GRAPHICS_ENABLE_AMBIENT_OCCLUSION = false;
    app_settings.GRAPHICS_ENABLE_DEPTH_OF_FIELD = false;
    app_settings.GRAPHICS_ENABLE_BLOOM = false;
    app_settings.GRAPHICS_ENABLE_TONE_MAPPING = true;
    app_settings.GRAPHICS_ENABLE_HDR = false;

    #[cfg(not(target_arch = "wasm32"))]
    {
        app_settings.GRAPHICS_MSAA_SAMPLES = GraphicsMsaaSamples::Msaa_Sample2;
    }

    #[cfg(target_arch = "wasm32")]
    {
        app_settings.GRAPHICS_MSAA_SAMPLES = GraphicsMsaaSamples::Msaa_Sample4;
    }

    app_settings.GRAPHICS_SHADOW_FILTER = GraphicShadowFilteringMethod::ShadowFiltering_Hardware2x2;
    app_settings.GRAPHICS_ENABLE_ANIMATIONS = true;
    app_settings.GRAPHICS_ENABLE_LIGHTS = true;
    app_settings
}

fn on_preset_high_graphics(input: &AppSettings) -> AppSettings {
    let mut app_settings = input.clone();

    app_settings.GRAPHICS_USE_LOW_POLY_MODELS = false;
    app_settings.GRAPHICS_ENABLE_MOTION_BLUR = false;
    app_settings.GRAPHICS_ENABLE_ALL_PARTICLES = true;
    app_settings.GRAPHICS_ENABLE_FOG = true;
    app_settings.GRAPHICS_ENABLE_GLOW = true;
    app_settings.GRAPHICS_ENABLE_AVATARS = true;
    app_settings.GRAPHICS_ENABLE_ANTIALIAS = true;
    app_settings.GRAPHICS_ENABLE_SHADOWS = true;
    app_settings.GRAPHICS_ENABLE_PHYSICS = true;
    app_settings.GRAPHICS_ENABLE_AMBIENT_OCCLUSION = true;
    app_settings.GRAPHICS_ENABLE_DEPTH_OF_FIELD = false;
    app_settings.GRAPHICS_ENABLE_BLOOM = true;
    app_settings.GRAPHICS_ENABLE_TONE_MAPPING = true;
    app_settings.GRAPHICS_ENABLE_HDR = false;
    app_settings.GRAPHICS_MSAA_SAMPLES = GraphicsMsaaSamples::Msaa_Sample4;
    app_settings.GRAPHICS_SHADOW_FILTER = GraphicShadowFilteringMethod::ShadowFiltering_Gaussian;
    app_settings.GRAPHICS_ENABLE_ANIMATIONS = true;
    app_settings.GRAPHICS_ENABLE_LIGHTS = true;
    app_settings
}

fn on_preset_ultra_graphics(input: &AppSettings) -> AppSettings {
    let mut app_settings = input.clone();

    app_settings.GRAPHICS_USE_LOW_POLY_MODELS = false;
    app_settings.GRAPHICS_ENABLE_MOTION_BLUR = false;
    app_settings.GRAPHICS_ENABLE_ALL_PARTICLES = true;
    app_settings.GRAPHICS_ENABLE_FOG = true;
    app_settings.GRAPHICS_ENABLE_GLOW = true;
    app_settings.GRAPHICS_ENABLE_AVATARS = true;
    app_settings.GRAPHICS_ENABLE_ANTIALIAS = true;
    app_settings.GRAPHICS_ENABLE_SHADOWS = true;
    app_settings.GRAPHICS_ENABLE_PHYSICS = true;
    app_settings.GRAPHICS_ENABLE_AMBIENT_OCCLUSION = true;
    app_settings.GRAPHICS_ENABLE_DEPTH_OF_FIELD = true;
    app_settings.GRAPHICS_ENABLE_BLOOM = true;
    app_settings.GRAPHICS_ENABLE_TONE_MAPPING = true;
    app_settings.GRAPHICS_ENABLE_HDR = true;
    app_settings.GRAPHICS_MSAA_SAMPLES = GraphicsMsaaSamples::Msaa_Sample8;
    app_settings.GRAPHICS_SHADOW_FILTER = GraphicShadowFilteringMethod::ShadowFiltering_Temporal;
    app_settings.GRAPHICS_ENABLE_ANIMATIONS = true;
    app_settings.GRAPHICS_ENABLE_LIGHTS = true;
    app_settings
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn given_true_trigger_offline_action_when_state_is_not_offline_then_should_be_offline() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::TriggerOfflineMode);
        action.set_boolValue(true);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.is_offline, true);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.events[0], AppStateEvents::OfflineModeEnabled);
    }

    #[test]
    fn given_false_trigger_offline_action_when_state_is_offline_then_should_not_be_offline() {
        let mut app_state = AppState::default();
        app_state.is_offline = true;
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::TriggerOfflineMode);
        action.set_boolValue(false);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.is_offline, false);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.events[0], AppStateEvents::OfflineModeDisabled);
    }

    #[test]
    fn given_disable_ui_action_when_ui_is_enabled_then_should_disable_ui() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::DisableUI);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.disable_all_ui, true);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.events[0], AppStateEvents::UIDisabled);
    }

    #[test]
    fn given_enable_ui_action_when_ui_is_disabled_then_should_enable_ui() {
        let mut app_state = AppState::default();
        app_state.disable_all_ui = true;
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::EnableUI);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.disable_all_ui, false);
    }

    #[test]
    fn given_set_is_mobile_action_when_is_mobile_is_false_then_should_set_is_mobile() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetIsMobile);
        action.set_boolValue(false);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.is_mobile, false);
    }

    #[test]
    fn given_set_canvas_loaded_action_when_canvas_is_not_loaded_then_should_set_canvas_loaded() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetCanvasLoaded);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.canvas_loaded, true);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.events[0], AppStateEvents::CanvasLoaded);
    }

    #[test]
    fn given_set_logged_in_action_when_logged_in_is_false_then_should_set_logged_in() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetLoggedIn);
        action.set_boolValue(true);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.logged_in, true);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.events[0], AppStateEvents::ClientLoggedIn);
    }

    #[test]
    fn given_set_logged_out_action_when_logged_in_is_true_then_should_set_logged_out() {
        let mut app_state = AppState::default();
        app_state.logged_in = true;
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetLoggedIn);
        action.set_boolValue(false);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.logged_in, false);
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.events[0], AppStateEvents::ClientLoggedOut);
    }

    #[test]
    fn given_set_settings_from_local_storage_action_when_settings_are_different_then_should_set_settings() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);

        let mut settings = AppSettings::default();
        settings.set_AUDIO_ENABLE_REVERB(true);
        settings.set_AUDIO_MIDI_OUTPUT_ONLY(true);
        action.set_appSettings(settings.clone());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.app_settings.AUDIO_ENABLE_REVERB, settings.AUDIO_ENABLE_REVERB);
        assert_eq!(result.state.app_settings.AUDIO_MIDI_OUTPUT_ONLY, settings.AUDIO_MIDI_OUTPUT_ONLY);
        assert_eq!(result.effects.len(), 1);
        assert_eq!(result.effects[0].action, AppStateEffects_Action::AppSettingsUpdated);
    }

    #[test]
    fn given_set_settings_from_local_storage_action_when_settings_are_same_then_should_not_set_settings() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);
        action.set_appSettings(AppSettings::default());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.app_settings, AppSettings::default());
        assert_eq!(result.effects.len(), 0);
    }

    #[test]
    fn given_reset_state_action_when_state_is_not_default_then_should_reset_state() {
        let mut app_state = AppState::default();
        app_state.is_offline = true;
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::ResetState);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(*result.state, AppState::default());
        assert_eq!(result.events.len(), 1);
        assert_eq!(result.events[0], AppStateEvents::AppStateReset);
    }

    #[test]
    fn given_initialize_app_state_action_when_state_is_initialized_then_should_be_updated() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::InitializeAppState);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state.clone()), &action);

        assert_eq!(result.state.initialized, true);
    }

    #[test]
    fn given_unhandled_action_when_state_is_not_changed_then_should_do_nothing() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::default());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state.clone()), &action);

        assert_eq!(*result.state, app_state);
    }

    #[test]
    fn given_unhandled_action_when_state_is_changed_then_should_do_nothing() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::default());
        action.set_boolValue(true);

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state.clone()), &action);

        assert_eq!(*result.state, app_state);
    }

    #[test]
    fn given_set_app_settings_action_when_settings_are_same_then_should_not_set_settings() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);
        action.set_appSettings(AppSettings::default());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.app_settings, AppSettings::default());
        assert_eq!(result.effects.len(), 0);
    }

    #[test]
    fn given_set_app_settings_action_when_graphics_preset_is_low_then_should_set_low_graphics() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);

        let mut settings = AppSettings::default();
        settings.set_GRAPHICS_PRESET(GraphicsPresets::Preset_Low);
        action.set_appSettings(settings.clone());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        settings = on_preset_low_graphics(&settings);
        assert_eq!(result.state.app_settings, settings);
    }

    #[test]
    fn given_set_app_settings_action_when_graphics_preset_is_medium_then_should_set_medium_graphics() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);

        let mut settings = AppSettings::default();
        settings.set_GRAPHICS_PRESET(GraphicsPresets::Preset_Medium);
        action.set_appSettings(settings.clone());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        settings = on_preset_medium_graphics(&settings);
        assert_eq!(result.state.app_settings, settings);
    }

    #[test]
    fn given_set_app_settings_action_when_graphics_preset_is_high_then_should_set_high_graphics() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);

        let mut settings = AppSettings::default();
        settings.set_GRAPHICS_PRESET(GraphicsPresets::Preset_High);
        action.set_appSettings(settings.clone());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        settings = on_preset_high_graphics(&settings);
        assert_eq!(result.state.app_settings, settings);
    }

    #[test]
    fn given_set_app_settings_action_when_graphics_preset_is_ultra_then_should_set_ultra_graphics() {
        let mut app_state = AppState::default();
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);

        let mut settings = AppSettings::default();
        settings.set_GRAPHICS_PRESET(GraphicsPresets::Preset_Ultra);
        action.set_appSettings(settings.clone());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        settings = on_preset_ultra_graphics(&settings);
        assert_eq!(result.state.app_settings, settings);
    }

    #[test]
    fn given_set_app_settings_actions_when_graphic_setting_change_and_does_not_match_predefined_preset_then_should_graphics_preset_to_custom()
    {
        let mut app_state = AppState::default();
        app_state.app_settings.GRAPHICS_PRESET = GraphicsPresets::Preset_Ultra;

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::SetAppSettings);

        let mut settings = AppSettings::default();
        settings.set_GRAPHICS_PRESET(app_state.app_settings.GRAPHICS_PRESET);
        settings.set_GRAPHICS_ENABLE_MOTION_BLUR(true);
        settings.set_GRAPHICS_SHADOW_FILTER(GraphicShadowFilteringMethod::ShadowFiltering_Hardware2x2);
        action.set_appSettings(settings.clone());

        let reducer = AppStateReducer::default();
        let result = reducer.reduce(&Rc::new(app_state), &action);

        assert_eq!(result.state.app_settings.GRAPHICS_PRESET, GraphicsPresets::Preset_Custom);
    }
}
