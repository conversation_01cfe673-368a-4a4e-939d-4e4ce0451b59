// @refresh reload
import 'nouislider/dist/nouislider.css';
import "vanilla-cookieconsent/dist/cookieconsent.css";
import "~/packages/solid-contextmenu/scss/main.scss";
import "~/sass/index.css";
import "~/sass/index.sass";
import "~/sass/index.scss";
import "~/sass/no-ui-slider.scss";
import "~/sass/sweetalert.sass";
import "~/sass/variables.css";
import "~/sass/vendors.sass";
import "~/sass/vendors.extensions.scss";

import { Box, Center, HopeProvider, VStack } from "@hope-ui/solid";
import { MetaProvider, Title } from "@solidjs/meta";
import { Route, Router } from "@solidjs/router";
import { FileRoutes } from "@solidjs/start/router";
import { Component, ErrorBoundary, lazy, onMount, Show, Suspense } from "solid-js";
import { Presence } from "solid-motionone";
import { ServiceRegistry, useService } from "solid-services";
import { Toaster } from "solid-toast";
import { I18nProvider } from "./i18n/i18nProvider";
import AppService from './services/app.service';
import ThemeConfig from "./util/theme-config";
import I18nService from './services/i18n.service';

const Pageloader = lazy(() => import('~/components/pageloader'));

const PagerLoaderRoot = () => {
  const appService = useService(AppService);

  return (
    <Presence exitBeforeEnter>
      <Show when={appService().activatePageLoader() || appService().pagerLoaderAnimating()}>
        <Suspense>
          <Pageloader />
        </Suspense>
      </Show>
    </Presence>
  );
};

type FailedToLoadAppProps = { error?: any; };

const FailedToLoadApp: Component<FailedToLoadAppProps> = (props) => {
  onMount(() => { console.error(props.error); });

  return (
    <Box w="100vw" h="100dvh" overflow={"scroll"}>
      <Center>
        <VStack spacing="$2" textAlign={"center"}>
          <Box as="h1">Oops! Something went wrong. 💥</Box>
          <Box as="h2">Please check the console logs for more details and then reload the app.</Box>
          <br />
          <Show when={props.error}>
            <pre>{props.error.message}</pre>
          </Show>
        </VStack>
      </Center>
    </Box>
  );
};

const AppInitialization = () => {
  const appService = useService(AppService);
  const i18nService = useService(I18nService);

  onMount(() => {
    i18nService().initialize(appService().appStateEffects);
  });

  return undefined;
};

export default function App() {
  return (
    <HopeProvider config={ThemeConfig} >
      <Suspense fallback="Loading...">
        <ErrorBoundary fallback={(err) => <FailedToLoadApp error={err} />}>
          <Toaster />
          <Router
            root={props => (
              <MetaProvider>
                <Title>PianoRhythm</Title>
                <ErrorBoundary fallback={(err) => <FailedToLoadApp error={err} />}>
                  <ServiceRegistry>
                    <I18nProvider>
                      <AppInitialization />
                      <PagerLoaderRoot />
                      <Suspense>
                        {props.children}
                      </Suspense>
                    </I18nProvider>
                  </ServiceRegistry>
                </ErrorBoundary>
              </MetaProvider>
            )}
          >
            <FileRoutes />
            <Route path={["/room", "/room/:roomName"]} component={lazy(() => import("~/components/pages/room.page"))} />
            <Route path={["/"]} component={lazy(() => import("~/routes/login"))} />
            <Route path="*" component={lazy(() => import("~/routes/404"))} />
          </Router>
        </ErrorBoundary>
      </Suspense>
    </HopeProvider>
  );
}
