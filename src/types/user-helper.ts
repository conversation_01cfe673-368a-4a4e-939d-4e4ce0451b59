import {
  Badges,
  ClientSideUserDto,
  Roles,
  UserBadges,
  UserClientDto,
  UserDto,
  UserStatus,
  UserUpdateCommand,
  badgesFromJSON,
  badgesToJSON,
  rolesFromJSON,
  rolesToJSON
} from '~/proto/user-renditions';
import { COMMON, IMAGES } from "~/util/const.common";
import { cloneDeep, isDate, isNaN, isNumber, isString, mapValues, orderBy } from "lodash-es";
import {
  ApiUserDto,
  ClientMetaDetails,
  ClientSideUserDtoHelper,
  MOD_USER_ROLES,
  StatusColor
} from "~/types/user.types";

export const mapRoleRank = (role: Roles): number => {
  switch (role) {
    case Roles.ADMIN:
      return 999;
    case Roles.SYSTEM:
      return mapRoleRank(Roles.ADMIN) - 1;
    case Roles.DEVELOPER:
      return mapRoleRank(Roles.GUEST) - 1;
    case Roles.MODERATOR:
    case Roles.TRIAL_MODERATOR:
      return mapRoleRank(Roles.GUEST) + 1;
    case Roles.HELPBOT:
    case Roles.BOT:
      return mapRoleRank(Roles.GUEST) + 2;
    case Roles.GUEST:
    case Roles.DISCORDBOT:
      return mapRoleRank(Roles.ROOMOWNER) + 1;
    case Roles.ROOMOWNER:
      return mapRoleRank(Roles.MEMBER) + 1;
    case Roles.MEMBER:
      return 1;
    default:
      return 0;
  }
};

const _getUserProfileImage = (usertag?: string, lastModified?: Date | string) => {
  if (!usertag) return IMAGES.DEFAULT_PROFILE_IMAGE;

  try {
    let date = Date.parse(lastModified as any);
    let cacheBust = isNumber(date) && !isNaN(date) ? `?cb=${date}` : ``;
    return `${COMMON.WS_HOST}/api/users/profile-image/${encodeURIComponent(usertag || "")}${cacheBust}`;
  } catch {
    return IMAGES.DEFAULT_PROFILE_IMAGE;
  }
};

// export const getUserProfileImage = memoize<(usertag?: string, lastModified?: Date | string) => string>(_getUserProfileImage);
export const getUserProfileImage = _getUserProfileImage;

export const getUserProfileBackgroundImage = (usertag?: string, lastModified?: Date | string) => {
  if (!usertag) return IMAGES.DEFAULT_PROFILE_IMAGE;

  try {
    let date = Date.parse(lastModified as any);
    let cacheBust = isNumber(date) && !isNaN(date) ? `?cb=${date}` : ``;
    return `${COMMON.WS_HOST}/api/users/profile-background-image/${encodeURIComponent(usertag || "")}${cacheBust}`;
  } catch {
    return IMAGES.DEFAULT_PROFILE_IMAGE;
  }
};

export class UserClientDomain {
  private dto: UserClientDto;
  private clientMetaDetailsParsed: ClientMetaDetails | undefined = undefined;

  constructor(dto?: UserClientDto) {
    this.dto = dto ?? UserClientDto.create();
    this.sanitize();
  }

  private sanitize() {
    if (this.dto.meta?.billingSettings) {
      let billingMeta = this.dto.meta.billingSettings.meta;
      let billingDate = billingMeta?.nextBillingDate;
      // if (billingMeta && billingDate) billingMeta.nextBillingDate = new Date(billingDate);
    }

    let meta = this.dto.userDto?.meta;
    if (meta && isString(meta.clientMetaDetails)) {
      this.clientMetaDetailsParsed = ClientSideUserDtoHelper.GetParsedMetaDetails(meta.clientMetaDetails);
    }

    // Handles empty strings
    this.dto.userDto = mapValues(this.dto.userDto, v => (v == "" || v == null) ? undefined : v) as any;
  }

  updateClient(dto: UserClientDto) {
    this.dto = dto;
    this.sanitize();
    return this;
  }

  updateUserDto(value: ClientSideUserDto) {
    this.dto.userDto = value.userDto;
    this.sanitize();
  }

  updateByCommand(command: UserUpdateCommand) {
    let updated = ClientSideUserDtoHelper.updateUserDtoByCommand(this.getClientSideUserDto(), command);
    this.dto.userDto = updated.userDto;
    this.sanitize();
  }

  getDto(): UserClientDto {
    return { ...this.dto };
  }

  getUserDto(): UserDto {
    return cloneDeep(this.dto.userDto || UserDto.create());
  }

  getRoles() {
    return this.getUserDto()?.roles as Roles[];
  }

  getClientSideUserDto(): ClientSideUserDto {
    let dto = ClientSideUserDto.create({
      socketID: this.socketID,
      userDto: this.dto.userDto
    });

    return dto;
  }

  toApiUserDto() {
    return UserDtoUtil.clientSideUserDtoToApiUserDto(this.getClientSideUserDto());
  }

  getClientMetaDetails() {
    return this.clientMetaDetailsParsed;
  }

  getOrchestraModelCustomizationDataJSON() {
    return this.dto.userDto?.worldData?.orchestraModelCustomizationDataJSON;
  }

  get email() {
    return this.getDto().meta?.email;
  }

  get isOAuthAccount() {
    return (
      this.dto.isOAuthAccount
    );
  }

  get serverChatMuted() {
    return this.getUserDto()?.serverChatMuted;
  }

  get status() {
    return this.getUserDto()?.status;
  }

  get serverNotesMuted() {
    return this.getUserDto()?.serverNotesMuted;
  }

  get isDeveloper() {
    return UserDtoUtil.isDeveloperExactly(this.getRoles());
  }

  get isAtleastDeveloper() {
    return UserDtoUtil.isAtleastDeveloper(this.getRoles());
  }

  get isGuest() {
    return UserDtoUtil.isGuest(this.getRoles());
  }

  get isMember() {
    return UserDtoUtil.isMember(this.getRoles());
  }

  get isMod() {
    return UserDtoUtil.isMod(this.getRoles());
  }

  get isAdmin() {
    return UserDtoUtil.isAdminExactly(this.getRoles());
  }

  get socketID() {
    return this.getUserDto()?.socketID;
  }

  get username() {
    return this.getUserDto()?.username;
  }

  get usertag() {
    return this.getUserDto()?.usertag;
  }

  get usercolor() {
    return this.getUserDto().color;
  }

  get isProMember() {
    return this.getUserDto().isProMember;
  }

  get canUseProFeature() {
    return this.isProMember || this.isMod;
  }

  get hasRainbowBorder() {
    return this.canUseProFeature && this.usercolor?.toLowerCase() == "rainbow";
  }

  get profileImagePath() {
    return getUserProfileImage(this.usertag);
  }

  get nextBillingDate() {
    let nextBillingDate = this.getDto().meta?.billingSettings?.meta?.nextBillingDate;

    try {
      if (nextBillingDate && typeof nextBillingDate == "string") {
        let date = new Date(nextBillingDate);
        return date.toDateString();
      } else if (nextBillingDate && isDate(nextBillingDate)) {
        return nextBillingDate.toDateString();
      }

    } catch {
    }

    return "N/A";
  }

  get billingCurrencyText() {
    let user = this.getDto();
    let meta = user.meta?.billingSettings?.meta;
    // return meta?.currencyCode && meta?.pricePerUnit ? `${getSymbolFromCurrency(meta.currencyCode?.toUpperCase())}${meta.pricePerUnit}` : "N/A";
    return meta;
  }

  get billingCancellationInProcess() {
    return this.getDto().meta?.billingSettings?.cancelationInProcess;
  }

  get hasProPlan() {
    return this.getDto().meta?.billingSettings?.currentPlan.toLowerCase() != "free";
  }

  get currentBillingPlan() {
    return this.getDto().meta?.billingSettings?.currentPlan || "Free";
  }

  get billingMeta() {
    return this.getDto().meta?.billingSettings?.meta;
  }
}

export class UserDtoUtil {
  static isAtleastRank = (roles: Roles[] | undefined, targetRole: Roles) => {
    if (roles == undefined || roles.length == 0) return false;
    let highestRole = RolesHelper.getHighestRole(roles);
    if (!highestRole)
      return false;
    return highestRole.rank >= mapRoleRank(targetRole);
  };

  static isMod = (roles?: Roles[]) => (roles && roles.some(v => MOD_USER_ROLES.includes(v))) || false;
  static hasRole = (roles: Roles[] | undefined, role: Roles) => roles?.includes(role) || false;
  static isModExactly = (roles: Roles[] | undefined) => roles?.includes(Roles.MODERATOR) || false;
  static isTrialModExactly = (roles: Roles[] | undefined) => roles?.includes(Roles.TRIAL_MODERATOR) || false;
  static isAdminExactly = (roles: Roles[] | undefined) => roles?.includes(Roles.ADMIN) || false;
  static isDeveloperExactly = (roles: Roles[] | undefined) => roles?.includes(Roles.DEVELOPER);
  static isAtleastDeveloper = (roles: Roles[] | undefined) => UserDtoUtil.isDeveloperExactly(roles) || UserDtoUtil.isAtleastRank(roles, Roles.DEVELOPER);
  static isRoomOwner = (roles: Roles[] | undefined) => roles?.includes(Roles.ROOMOWNER);
  static isGuest = (roles: Roles[] | undefined) => roles?.length == 0 || roles?.includes(Roles.GUEST) || false;
  static isBot = (roles: Roles[] | undefined) => roles?.includes(Roles.BOT) || roles?.includes(Roles.HELPBOT) || roles?.includes(Roles.DISCORDBOT) || false;
  static isSystem = (roles: Roles[] | undefined) => roles?.includes(Roles.SYSTEM) || false;
  static isMember = (roles: Roles[] | undefined) => (roles?.includes(Roles.MEMBER) || UserDtoUtil.isAtleastRank(roles, Roles.MEMBER)) &&
    !this.isBot(roles) && !this.isGuest(roles);
  static cannotGetProfileImage = (roles: Roles[] | undefined) => [this.isBot(roles), this.isSystem(roles), this.isGuest(roles)].some(x => x);
  static getUserProfileImage = (usertag?: string, lastModified?: Date | string) => getUserProfileImage(usertag, lastModified);
  static getUserProfileBackgroundImage = (usertag?: string, lastModified?: Date | string) => getUserProfileBackgroundImage(usertag, lastModified);
  static getUserSideBarElementID = (dto: ClientSideUserDto) => `pr-sidebar-user-element-${dto.userDto!.socketID}`;
  static getUserStatusColor = (status?: UserStatus) => {
    if (!status)
      return StatusColor.Unknown;
    switch (status) {
      case UserStatus.Online:
        return StatusColor.Online;
      case UserStatus.Idle:
        return StatusColor.Idle;
      case UserStatus.DoNotDisturb:
        return StatusColor.DoNotDisturb;
      case UserStatus.AFK:
        return StatusColor.AFK;
      default:
        return StatusColor.Unknown;
    }
  };

  static clientSideUserDtoToApiUserDto = (input?: ClientSideUserDto): ApiUserDto | undefined => {
    if (!input) return;
    let dto = input.userDto!;

    return {
      username: dto.username,
      nickname: dto.nickname,
      usertag: dto.usertag,
      roles: dto.roles.map(rolesToJSON),
      color: dto.color,
      statusText: dto.statusText,
      profileDescription: dto.ProfileDescription,
      lastOnline: undefined,
      profileImageLastModified: dto.profileImageLastModified ? new Date(dto.profileImageLastModified) : undefined,
      profileBackgroundImageLastModified: dto.profileBackgroundImageLastModified ? new Date(dto.profileBackgroundImageLastModified) : undefined,
      badges: dto.badges.map(BadgesHelper.userBadgesToString)
    };
  };
}

export namespace RolesHelper {
  type RoleWithRank = { role: Roles; rank: number; };

  export const getHighestRole = (roles: Roles[]): RoleWithRank | undefined => {
    let role = orderBy(roles.map(x => ({ role: x, rank: mapRoleRank(x) })), "rank", "desc").shift();
    return role;
  };

  export const mapRolesFromJSON = (roles: string[]) => roles.map(rolesFromJSON);
  export const mapRolesToJSON = (roles: Roles[]) => roles.map(rolesToJSON);
}

export namespace BadgesHelper {
  export const userBadgesToString = (x: UserBadges): string => {
    let badgeJson = badgesToJSON(x.badge);
    switch (x.badge) {
      case Badges.CUSTOM:
      case Badges.TranslationContributor:
        return `${badgeJson}: ${x.value}`;
      default:
        return badgeJson;
    }
  };

  export const userBadgesFromString = (x: string): UserBadges => {
    if (!x) return UserBadges.create({ badge: Badges.UNRECOGNIZED });

    let upper = x.toUpperCase();

    if (upper.startsWith("CUSTOM")) {
      let value = x.split(":")[1];
      return UserBadges.create({ badge: Badges.CUSTOM, value: value.trim() });
    }

    if (upper.startsWith("TRANSLATIONCONTRIBUTOR")) {
      let value = x.split(":")[1];
      return UserBadges.create({ badge: Badges.TranslationContributor, value: value.trim() });
    }

    return UserBadges.create({ badge: badgesFromJSON(x) });
  };

  export const getName = (x: UserBadges) => {
    let name = () => {
      let badgeJson = badgesToJSON(x.badge);
      switch (x.badge) {
        case Badges.CUSTOM:
          return `${x.value?.trim()}`;
        case Badges.TranslationContributor:
          return `Translation Contributor: ${x.value?.trim()}`;
        case Badges.TranslationMasterContributor:
          return "Translation Master Contributor";
        default:
          return badgeJson;
      }
    };

    return name().replaceAll("_", " ");
  };
}

export namespace UserClientDtoHelper {
  export const DEFAULT: UserClientDto = UserClientDto.create();

  export const ToClientSideUserDto = (input: UserClientDto): ClientSideUserDto => {
    return ClientSideUserDto.create({ userDto: input.userDto });
  };
}

export namespace BillingSettingsHelper {
  export const RankPlan = (plan: string) => {
    switch (plan.toLowerCase()) {
      case "free":
        return 0;
      case "level1":
        return 1;
      case "level2":
        return 2;
      case "level3":
        return 3;
      default:
        return -1;
    }
  };
}