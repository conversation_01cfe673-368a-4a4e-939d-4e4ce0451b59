import { Db } from "mongodb";
import { UserDbo, UserDboZ } from "~/models/user-dbo.models";
import { Database, IBaseDBService } from "../db-store";
import { Roles, rolesFromJSON, rolesToJSON } from "~/proto/user-renditions";
import { mapRoleRank } from "~/types/user-helper";

export class UserDBService implements IBaseDBService<UserDbo> {
  db: Db;
  public static collectionName = "users";
  private static instance: UserDBService;

  private constructor() {
    this.db = Database.getInstance().getDb();
  }

  public static getInstance() {
    if (!UserDBService.instance) {
      UserDBService.instance = new UserDBService();
    }

    return UserDBService.instance;
  }

  private getCollection() {
    return this.db.collection<UserDbo>(UserDBService.collectionName);
  }

  private parseUser(user: UserDbo | any) {
    let parsed = UserDboZ.safeParse(user);
    if (parsed.success) {
      return parsed.data;
    }
    return null;
  }

  private async getUserById(uuid: string) {
    return this.getCollection().findOne({ uuid }).then(this.parseUser);
  }

  private async getUserByUsername(username: string) {
    return this.getCollection().findOne({ username }).then(this.parseUser);
  }

  private async getUserByUsertag(usertag: string) {
    return this.getCollection().findOne({ "info.tag": usertag }).then(this.parseUser);
  }

  private async getUserByEmail(email: string) {
    return this.getCollection().findOne({ "info.email": email }).then(this.parseUser);
  }

  private async aggregateUsers<T = any>(pipeline: object[]) {
    try {
      return this.getCollection().aggregate(pipeline).toArray() as Promise<T[]>;
    } catch {
      return Promise.resolve([]);
    }
  }

  parseManyUsers(users: any[]) {
    return users.map(this.parseUser).filter(Boolean) as UserDbo[];
  }

  async aggregateData<R = any>(pipeline: object[]): Promise<R[]> {
    return this.aggregateUsers<R>(pipeline);
  }

  async getById(id: string): Promise<UserDbo | null> {
    return this.getUserById(id);
  }

  async getByUsername(username: string): Promise<UserDbo | null> {
    return this.getUserByUsername(username);
  }

  async getByUsertag(usertag: string): Promise<UserDbo | null> {
    return this.getUserByUsertag(usertag);
  }

  async getByEmail(email: string): Promise<UserDbo | null> {
    return this.getUserByEmail(email);
  }

  async createUser(userData: Omit<UserDbo, '_id'>): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().insertOne(userData as UserDbo);
      if (result.insertedId) {
        return this.getUserById(userData.uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error creating user:', error);
      return null;
    }
  }

  async updateUser(uuid: string, updateData: Partial<UserDbo>): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().updateOne(
        { uuid },
        {
          $set: {
            ...updateData,
            modified: new Date()
          }
        }
      );

      if (result.modifiedCount > 0) {
        return this.getUserById(uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error updating user:', error);
      return null;
    }
  }

  async deleteUser(uuid: string): Promise<boolean> {
    try {
      const result = await this.getCollection().deleteOne({ uuid });
      return result.deletedCount > 0;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error deleting user:', error);
      return false;
    }
  }

  async verifyEmail(uuid: string): Promise<UserDbo | null> {
    return this.updateUser(uuid, { emailVerified: true });
  }

  async updateUserSettings(uuid: string, settings: Partial<UserDbo['settings']>): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().updateOne(
        { uuid },
        {
          $set: {
            settings: settings,
            modified: new Date()
          }
        }
      );

      if (result.modifiedCount > 0) {
        return this.getUserById(uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error updating user settings:', error);
      return null;
    }
  }

  async updateUserPermissions(uuid: string, permissions: Partial<UserDbo['permissions']>): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().updateOne(
        { uuid },
        {
          $set: {
            permissions: permissions,
            modified: new Date()
          }
        }
      );

      if (result.modifiedCount > 0) {
        return this.getUserById(uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error updating user permissions:', error);
      return null;
    }
  }

  async addFriend(uuid: string, friendInfo: UserDbo['info']['friends'][0]): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().updateOne(
        { uuid },
        {
          $push: { "info.friends": friendInfo },
          $set: { modified: new Date() }
        }
      );

      if (result.modifiedCount > 0) {
        return this.getUserById(uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error adding friend:', error);
      return null;
    }
  }

  async removeFriend(uuid: string, friendID: string): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().updateOne(
        { uuid },
        {
          $pull: { "info.friends": { friendID } },
          $set: { modified: new Date() }
        }
      );

      if (result.modifiedCount > 0) {
        return this.getUserById(uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error removing friend:', error);
      return null;
    }
  }

  async blockUser(uuid: string, blockInfo: UserDbo['info']['blocked'][0]): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().updateOne(
        { uuid },
        {
          $push: { "info.blocked": blockInfo },
          $set: { modified: new Date() }
        }
      );

      if (result.modifiedCount > 0) {
        return this.getUserById(uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error blocking user:', error);
      return null;
    }
  }

  async unblockUser(uuid: string, blockedUsertag: string): Promise<UserDbo | null> {
    try {
      const result = await this.getCollection().updateOne(
        { uuid },
        {
          $pull: { "info.blocked": { usertag: blockedUsertag } },
          $set: { modified: new Date() }
        }
      );

      if (result.modifiedCount > 0) {
        return this.getUserById(uuid);
      }
      return null;
    } catch (error) {
      if (process.env.DEBUG) console.error('Error unblocking user:', error);
      return null;
    }
  }

  async searchUsers(searchTerm: string, limit: number = 20, skip: number = 0): Promise<UserDbo[]> {
    try {
      const pipeline = [
        {
          $match: {
            $or: [
              { username: { $regex: searchTerm, $options: 'i' } },
              { "info.tag": { $regex: searchTerm, $options: 'i' } },
              { "settings.nickname": { $regex: searchTerm, $options: 'i' } }
            ]
          }
        },
        { $skip: skip },
        { $limit: limit },
        { $sort: { modified: -1 } }
      ];

      const results = await this.aggregateUsers(pipeline);
      return this.parseManyUsers(results);
    } catch (error) {
      if (process.env.DEBUG) console.error('Error searching users:', error);
      return [];
    }
  }

  async userHasRole(usertag: string, role: Roles): Promise<boolean> {
    try {
      const user = await this.getUserByUsertag(usertag);

      // Check if user role is equal or higher than the target role
      if (!user) return false;
      if (user.info.roles.some(r => r.Case == rolesToJSON(role))) return true;
      return user.info.roles.some(r => mapRoleRank(rolesFromJSON(r.Case)) >= mapRoleRank(role));
    } catch (error) {
      if (process.env.DEBUG) console.error('Error checking user role:', error);
      return false;
    }
  }

  async userIsModerator(usertag: string): Promise<boolean> {
    return await this.userHasRole(usertag, Roles.MODERATOR);
  }

  async userHasSheetMusicEditAccess(usertag: string): Promise<boolean> {
    return await this.userIsModerator(usertag) || await this.userHasRole(usertag, Roles.SHEETMUSICEDITOR);
  }
}
