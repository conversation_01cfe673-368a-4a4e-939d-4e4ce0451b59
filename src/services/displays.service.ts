import { createEffect, createSignal } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { AppSoundFx2 } from "~/types/app.types";
import AppService from "./app.service";
import SoundEffectsService from "./sound-effects.service";
import AppSettingsService from "./settings-storage.service";
import { AppStateEvents } from "~/proto/pianorhythm-events";
import uniq from "lodash-es/uniq";

/**
 * Represents a collection of boolean flags indicating the visibility or availability of various displays in the application.
 * Each flag corresponds to a specific display in the application.
 */
export type Displays = {
  INSTRUMENT_SELECTION: boolean,
  INSTRUMENT_DOCK: boolean,
  MIDI_IO_MODAL: boolean,
  SETTINGS_MODAL: boolean,
  METRONOME_MODAL: boolean,
  SHEET_MUSIC_VIEWER: boolean,
  SHEET_MUSIC_REPO_MODAL: boolean,
  SHEET_MUSIC_UPLOAD_MODAL: boolean,
  SHEET_MUSIC_DETAIL_MODAL: boolean,
  SHORTCUT_BINDING_DISPLAY: boolean,
  CHAT_MESSAGES: boolean,
  BOTTOM_BAR_FULLY_SHOWN: boolean,
  SIDEBAR_LIST: boolean,
  ROOMS_LIST: boolean,
  FPS: boolean,
  NEW_ROOM_MODAL: boolean,
  UPDATE_ROOM_MODAL: boolean,
  ENTER_ROOM_PASSWORD_MODAL: boolean,
  IMAGE_UPLOADER_MODAL: boolean,
  MOD_DASHBOARD_MODAL: boolean,
  MIDI_PLAYER_UI: boolean,
  RENDERING_STATS: boolean,
  RELEASE_NOTES: boolean,
  MIDI_REPO_MODAL: boolean,
  DOCS_MODAL: boolean;
  USER_BADGES_MODAL: boolean;
  AUDIO_EQUALIZER_MODAL: boolean;
  STAGE_AUDIO_EFFECTS_EDIT_MODAL: boolean;
  BOTTOM_BAR: boolean;
  WORLD_AVATAR_CREATOR_SCREEN: boolean;
  CREDITS: boolean;
  AUDIO_REVERB_MODAL: boolean;
  KICKED_USERS_LIST: boolean;
  BG_MUSIC_PLAYER: boolean;
  SCENE_WIDGET_BUTTONS: boolean;
  ORCHESTRA_MODEL_CUSTOMIZATION: boolean;
  SET_CHANNEL_VOLUME_MODAL: boolean;
  SET_CHANNEL_PAN_MODAL: boolean;
  CHANNELS_SLIDERS_MODAL: boolean;
  BEAT_SEQUENCER: boolean;
  SIDEBAR_HELP_DOCS: boolean;
  LEADERBOARDS_MODAL: boolean;
  MIDI_MUSIC_UPLOAD_MODAL: boolean;
  SHEET_MUSIC_DETAILS_MODAL: boolean;
  MIDI_LOOPER: boolean;
  DEBUG_STATS: boolean;
  KEYBOARD_MAPPING_OVERLAY: boolean;
  SOUNDFONTS_LIST_MODAL: boolean;
};

/**
 * Array of solo displays.
 * Each item in the array represents a key of the Displays object.
 */
export const SOLO_DISPLAYS: (keyof Displays)[] = [
  // "AUDIO_REVERB_MODAL",
  "KICKED_USERS_LIST",
  "MIDI_IO_MODAL",
  "SETTINGS_MODAL",
  "METRONOME_MODAL",
  "SHEET_MUSIC_REPO_MODAL",
  "SHEET_MUSIC_UPLOAD_MODAL",
  "SHORTCUT_BINDING_DISPLAY",
  "NEW_ROOM_MODAL",
  "UPDATE_ROOM_MODAL",
  "ENTER_ROOM_PASSWORD_MODAL",
  "IMAGE_UPLOADER_MODAL",
  "MOD_DASHBOARD_MODAL",
  "MIDI_REPO_MODAL",
  "DOCS_MODAL",
  "USER_BADGES_MODAL",
  "WORLD_AVATAR_CREATOR_SCREEN",
  "ORCHESTRA_MODEL_CUSTOMIZATION",
  "SET_CHANNEL_VOLUME_MODAL",
  "SET_CHANNEL_PAN_MODAL",
  "CHANNELS_SLIDERS_MODAL",
  "BEAT_SEQUENCER",
  "LEADERBOARDS_MODAL",
  "MIDI_MUSIC_UPLOAD_MODAL"
];

/**
 * Array of modal displays to play sound effects.
 *
 * @remarks
 * This array contains the keys of the modal displays that should trigger sound effects when displayed.
 *
 * @typeParam keyof Displays - The keys of the `Displays` object.
 */
export const MODAL_DISPLAYS_TO_PLAY_SFX: (keyof Displays)[] = [
  "NEW_ROOM_MODAL",
  "UPDATE_ROOM_MODAL",
  "SETTINGS_MODAL",
  "DOCS_MODAL",
  "MIDI_IO_MODAL",
  "MIDI_REPO_MODAL",
  "IMAGE_UPLOADER_MODAL",
  "AUDIO_EQUALIZER_MODAL",
  "SHEET_MUSIC_REPO_MODAL",
  "SHEET_MUSIC_UPLOAD_MODAL",
  "SHORTCUT_BINDING_DISPLAY",
  "ENTER_ROOM_PASSWORD_MODAL",
  "AUDIO_REVERB_MODAL",
  "KICKED_USERS_LIST",
  "SET_CHANNEL_VOLUME_MODAL",
  "SET_CHANNEL_PAN_MODAL",
  "CHANNELS_SLIDERS_MODAL",
  "BEAT_SEQUENCER",
  "LEADERBOARDS_MODAL",
  "MIDI_MUSIC_UPLOAD_MODAL",
  "MOD_DASHBOARD_MODAL",
  "SOUNDFONTS_LIST_MODAL",
  "CREDITS"
];

/**
 * Array of keys representing the widget displays to play sound effects.
 *
 * @remarks
 * This array contains the keys of the widget displays that are eligible to play sound effects.
 * The order of the keys in the array determines the order in which the sound effects will be played.
 *
 * @typeParam keyof Displays - The type of the keys in the array.
 */
export const WIDGET_DISPLAYS_TO_PLAY_SFX: (keyof Displays)[] = [
  "SCENE_WIDGET_BUTTONS",
  "SHEET_MUSIC_VIEWER",
  "INSTRUMENT_SELECTION",
  "INSTRUMENT_DOCK",
  "MIDI_PLAYER_UI",
  "ORCHESTRA_MODEL_CUSTOMIZATION",
  "MIDI_LOOPER",
  "DEBUG_STATS"
];

export const WIDGET_DISPLAYS_TO_PLAY_CLOSE_SFX: (keyof Displays)[] = [
  "CHAT_MESSAGES"
];

/**
 * Represents the default displays configuration.
 * Each key represents a specific display option, and the corresponding value indicates whether the display is enabled or disabled.
 */
export const DEFAULT_DISPLAYS: Displays = {
  CHAT_MESSAGES: true,
  INSTRUMENT_DOCK: true,
  SIDEBAR_LIST: true,
  ROOMS_LIST: true,
  BOTTOM_BAR: true,
  FPS: true,
  INSTRUMENT_SELECTION: false,
  MIDI_IO_MODAL: false,
  SETTINGS_MODAL: false,
  METRONOME_MODAL: false,
  SHEET_MUSIC_VIEWER: false,
  SHEET_MUSIC_REPO_MODAL: false,
  SHEET_MUSIC_DETAIL_MODAL: false,
  SHORTCUT_BINDING_DISPLAY: false,
  BOTTOM_BAR_FULLY_SHOWN: false,
  NEW_ROOM_MODAL: false,
  UPDATE_ROOM_MODAL: false,
  ENTER_ROOM_PASSWORD_MODAL: false,
  IMAGE_UPLOADER_MODAL: false,
  MOD_DASHBOARD_MODAL: false,
  RENDERING_STATS: false,
  MIDI_PLAYER_UI: false,
  RELEASE_NOTES: false,
  MIDI_REPO_MODAL: false,
  SHEET_MUSIC_UPLOAD_MODAL: false,
  DOCS_MODAL: false,
  USER_BADGES_MODAL: false,
  AUDIO_EQUALIZER_MODAL: false,
  WORLD_AVATAR_CREATOR_SCREEN: false,
  CREDITS: false,
  AUDIO_REVERB_MODAL: false,
  KICKED_USERS_LIST: false,
  BG_MUSIC_PLAYER: true,
  ORCHESTRA_MODEL_CUSTOMIZATION: false,
  SET_CHANNEL_VOLUME_MODAL: false,
  SET_CHANNEL_PAN_MODAL: false,
  CHANNELS_SLIDERS_MODAL: false,
  BEAT_SEQUENCER: false,
  SIDEBAR_HELP_DOCS: false,
  LEADERBOARDS_MODAL: false,
  SCENE_WIDGET_BUTTONS: true,
  STAGE_AUDIO_EFFECTS_EDIT_MODAL: false,
  MIDI_MUSIC_UPLOAD_MODAL: false,
  SHEET_MUSIC_DETAILS_MODAL: false,
  MIDI_LOOPER: false,
  DEBUG_STATS: false,
  KEYBOARD_MAPPING_OVERLAY: false,
  SOUNDFONTS_LIST_MODAL: false
};

/**
 * DisplaysService is a service that manages the display states in the application.
 * It provides methods to control the visibility of different displays and modals.
 * The service also handles sound effects based on the display state changes.
 *
 * @returns An object containing various methods and properties to interact with the display states.
 */
export default function DisplaysService() {
  const sfxService = useService(SoundEffectsService);
  const appService = useService(AppService);
  const appSettingsService = useService(AppSettingsService);

  const [autoHideBottomBar, setAutoHideBottomBar] = createSignal(true);
  const [docsURLParameters, setDocsURLParameters] = createSignal<string>();
  const [sidebarDocsPath, setSidebarDocsPath] = createSignal<string>();
  const [docsModalTitle, setDocsModalTitle] = createSignal<string>();
  const [displays, setDisplays] = createStore<Displays>({ ...DEFAULT_DISPLAYS });
  const [anyModalsOpened, setAnyModalsOpened] = createSignal(false);
  const [lastModalDisplay, setLastModalDisplay] = createSignal<keyof Displays>();
  const [modalsOpened, setModalsOpened] = createSignal<(keyof Displays)[]>([]);
  const [initialized, setInitialized] = createSignal(false);

  const trackedDisplayValues = new Map<(keyof Displays), boolean>();

  const initialize = () => {
    if (initialized()) return;
    appService().appStateEvents.listen(async (event) => {
      switch (event) {
        case AppStateEvents.AppStateReset: {
          onDisconnect();
          break;
        }
      }
    });
    setInitialized(true);
  };

  function closeAllDisplays(name: keyof Displays, value: boolean) {
    if (SOLO_DISPLAYS.includes(name) && value)
      Object.keys(displays)
        .map(y => y as keyof Displays)
        .filter(x => SOLO_DISPLAYS.includes(x))
        .forEach(y => setDisplays(y, false));
  }

  const hideAll = () => {
    Object.keys(displays)
      .map(y => y as keyof Displays)
      .forEach(y => setDisplays(y, false));
  };

  /**
   * Sets the display state for a given name.
   *
   * @param name - The name of the display.
   * @param value - The new state value for the display.
   */
  const setDisplay = (name: keyof Displays, value: boolean) => {
    closeAllDisplays(name, value);
    setDisplays(name, value);
    if (value && (SOLO_DISPLAYS.includes(name as any) || name.toLowerCase().includes("modal"))) {
      setModalsOpened(v => uniq([...v, name]).slice(-3));
    }
  };

  /**
   * Sets the display of the main UI elements.
   * @param show - A boolean value indicating whether to show or hide the UI elements.
   */
  const setDisplayMainUI = (show: boolean) => {
    setDisplay("SIDEBAR_LIST", show);
    setDisplay("INSTRUMENT_DOCK", show);
    setDisplay("CHAT_MESSAGES", show);
    setDisplay("BOTTOM_BAR", show);
    setDisplay("SCENE_WIDGET_BUTTONS", show);
  };

  /**
   * Handles the disconnect event.
   * - Sets the auto hide bottom bar to false.
   * - Resets the documentation URL parameters.
   * - Resets the documentation modal title.
   * - Resets the displays to the default values.
   */
  const onDisconnect = () => {
    setAutoHideBottomBar(false);
    setDocsURLParameters();
    setDocsModalTitle();
    setDisplays(DEFAULT_DISPLAYS);
  };

  createEffect(() => {
    let current = displays;

    Object.entries(current)
      // .filter(([_]) => appService().canvasLoaded())
      .forEach(([name, value]) => {
        let key = name as keyof Displays;
        let lastValue = trackedDisplayValues.get(key);

        if (lastValue != null && lastValue != value) {
          if (MODAL_DISPLAYS_TO_PLAY_SFX.includes(key)) {
            sfxService().playSFX_ui2(value ? AppSoundFx2.OPEN_MENU : AppSoundFx2.CLOSE_MENU, { volume: 0.07 });
          }

          if (WIDGET_DISPLAYS_TO_PLAY_SFX.includes(key)) {
            sfxService().playSFX_ui2(value ? AppSoundFx2.PAUSE : AppSoundFx2.EXIT, { volume: 0.07 });
          }

          if (WIDGET_DISPLAYS_TO_PLAY_CLOSE_SFX.includes(key) && !value) {
            sfxService().playSFX_ui2(AppSoundFx2.EXIT, { volume: 0.07 });
          }
        }

        trackedDisplayValues.set(key, value);
      });
  });

  createEffect(() => {
    let anyModalsOpened = Object.entries(displays).some(([key, value]) => {
      return SOLO_DISPLAYS.includes(key as any) && value;
    });

    setAnyModalsOpened(anyModalsOpened);
  });

  createEffect(() => {
    setDisplay("CHAT_MESSAGES", appSettingsService().getSetting("DISPLAY_CHAT"));
    setDisplay("FPS", appSettingsService().getSetting("DISPLAY_FPS"));
    setDisplay("INSTRUMENT_DOCK", appSettingsService().getSetting("DISPLAY_INST_DOCK"));
    setDisplay("RENDERING_STATS", appSettingsService().getSetting("GRAPHICS_DISPLAY_RENDER_STATS"));
    setDisplay("SCENE_WIDGET_BUTTONS", appSettingsService().getSetting("DISPLAY_SCENE_WIDGET_BUTTONS"));
    setAutoHideBottomBar(appSettingsService().getSetting("AUTOHIDE_BOTTOMBAR"));
  });

  return {
    initialize,
    initialized,
    docsURLParameters,
    setDocsURLParameters,
    sidebarDocsPath,
    clearSidebarDocsPath: () => setSidebarDocsPath(),
    setSidebarDocsPath: (path: string, openModal = true) => {
      if (!path) return;
      if (path.indexOf("/") != 0) path = "/" + path;

      setSidebarDocsPath(path);
      setDisplay("SIDEBAR_HELP_DOCS", openModal);
    },
    docsModalTitle,
    setDocsModalTitle,
    autoHideBottomBar,
    setAutoHideBottomBar,
    displays,
    lastModalDisplay,
    modalsOpened,
    onDisconnect,
    getDisplay: (name: keyof Displays) => displays[name],
    hideAll,
    getLastModalOpened: (n: number = 1) => {
      return modalsOpened()[modalsOpened().length - n];
    },
    getLastModalsOpened: () => modalsOpened().slice(-3),
    setDisplay,
    setDisplayMainUI,
    anyModalsOpened,
    setAnyModalsOpened,
    toggleDisplay: (name: keyof Displays) => {
      setDisplay(name, !displays[name]);
    }
  };
}
