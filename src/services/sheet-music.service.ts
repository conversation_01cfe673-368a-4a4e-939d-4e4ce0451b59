import { SearchDriver } from "@elastic/search-ui";
import { createSignal } from "solid-js";
import { useService } from "solid-services";
import toast from "solid-toast";
import Swal from "sweetalert2";
import { getSheetMusicDetailsQuery } from "~/lib/sheet_music";
import {
  SheetMusicDetailResponse,
  SheetMusicDisapprovalReasons,
  SheetMusicDisapproveRequest,
  SheetMusicDto,
  SheetMusicRequest,
  SheetMusicUploadResponse
} from "~/models/sheet-music-dbo.models";
import { COMMON } from "~/util/const.common";
import SwalPR from "~/util/sweetalert";
import DisplaysService from "./displays.service";
import SoundEffectsService from "./sound-effects.service";
import { sheetMusicApprove, sheetMusicDelete, sheetMusicDisapprove, sheetMusicUpsert } from "~/server/sheet-music.api";

export function SheetMusicService() {
  const displayService = useService(DisplaysService);
  const sfxService = useService(SoundEffectsService);

  const [newSheetFromViewerUploadData, setNewSheetFromViewerUploadData] = createSignal<{ data?: string; }>();
  const [activeViewerData, setActiveViewerData] = createSignal<SheetMusicDetailResponse>();
  const [activeDetailID, setActiveDetailID] = createSignal<string>();
  const [_repoActive, setRepoActive] = createSignal(false);
  const [searchDriver, setSearchDriver] = createSignal<SearchDriver>();
  const [editMode, setEditMode] = createSignal(false);

  const getSheetMusicDetails = (id: string): Promise<SheetMusicDetailResponse | null> =>
    getSheetMusicDetailsQuery(id);

  const loadSheetMusic = async (id: string) => {
    let result = await getSheetMusicDetails(id);
    if (!result) return;

    setActiveViewerData(result);
    displayService().setDisplay("SHEET_MUSIC_VIEWER", true);
  };

  const sheetMusicUpsertBase = async (data: SheetMusicRequest, path = "create", generalErrorMsg = "Failed to upload sheet music.") => {
    let result: SheetMusicUploadResponse | undefined = undefined;

    if (path == "create") {
      let method = path == "create" ? "POST" : "PATCH";
      result = await sheetMusicUpsert(data, path, method);
    } else if (path.indexOf("update") == 0) {
      result = await sheetMusicUpsert(data, path, "PATCH");
    }

    if (result == undefined) {
      throw new Error(generalErrorMsg ?? "Failed to upload sheet music.");
    }

    let output = result as SheetMusicUploadResponse;

    if (output?.error) {
      if (output.error == "database_error_duplicate_key") {
        throw new Error("A sheet music with this title already exists under your account.");
      }

      throw new Error(output.error_description || output.error);
    }

    if (!output || !output.data) {
      throw new Error(generalErrorMsg || "Failed to upload sheet music.");
    }

    return output.data;
  };

  const uploadSheetMusic = async (data: SheetMusicRequest) => {
    if (!data) return Promise.reject("Failed to upload. No Sheet data was provided.");
    return sheetMusicUpsertBase(data, "create", "Failed to upload sheet music.");
  };

  const updateSheetMusic = async (data: SheetMusicRequest, id?: string) => {
    if (!id) return Promise.reject("Failed to update. No Sheet ID was provided.");
    return sheetMusicUpsertBase(data, `update/${id}`, "Failed to update sheet music.");
  };

  const clearActiveSheetMusicViewerData = () => {
    setActiveViewerData(undefined);
  };

  const approveSheetMusic = async (id: string) => {
    return sheetMusicApprove(id);
  };

  const disapproveSheetMusic = async (id: string, request: SheetMusicDisapproveRequest) => {
    return sheetMusicDisapprove(id, request);
  };

  const deleteSheetMusic = async (id: string) => {
    return sheetMusicDelete(id);
  };

  const sheetMusicIsFavorited = async (id: string, usertag: string) => {
    if (!id || !usertag) return false;

    try {
      let response = await fetch(`/api/v1/sheet_music/isFavorited?cb=${Date.now()}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        credentials: "include",
        body: JSON.stringify({ sheetMusicID: id, usertag })
      });

      return await response.json();
    } catch (e) {
      return false;
    }
  };

  const sheetMusicAddFavorite = async (id: string, usertag: string) => {
    return fetch(`${COMMON.EXPRESS_API_HOST}/api/v1/sheet_music/favorite/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sheetMusicID: id,
        usertag
      })
    }).then(res => res.json() as Promise<SheetMusicUploadResponse>);
  };

  const sheetMusicRemoveFavorite = async (id: string, usertag: string) => {
    return fetch(`${COMMON.EXPRESS_API_HOST}/api/v1/sheet_music/favorite/remove`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sheetMusicID: id,
        usertag
      })
    }).then(res => res.json() as Promise<SheetMusicUploadResponse>);
  };

  const deleteSheetMusicDialogue = (item: {
    title: string,
    id: string,
    creatorUsername: string;
  }, onSuccess?: (id: string) => void, onError?: (id: string) => void) => {
    SwalPR(sfxService).fire({
      icon: "warning",
      html: `
        Are you sure you want to <span style='color:red'><b>delete</b></span> this?
        <br><br>
        <div><b>ID</b>: ${item.id}</div>
        <div><b>Title</b>: ${item.title}</div>
        <div><b>Uploader</b>: ${item.creatorUsername}</div>
      `,
      showCancelButton: true,
      showLoaderOnConfirm: true,
      preConfirm: () => {
        return deleteSheetMusic(item.id);
      }
    })
      .then((result) => {
        if (result.isConfirmed) {
          SwalPR(sfxService).fire({
            icon: "success",
            html: `<b>${item.title}</b> has been deleted!`
          });
          refreshRepoView();
          if (onSuccess) onSuccess(item.id);
        }
      })
      .catch(() => {
        SwalPR(sfxService).fire({
          icon: "error",
          text: `Oof! Something went wrong. Failed to delete`
        });
        if (onError) onError(item.id);
      });
  };

  const startApprovalProcess = (item: SheetMusicDto, type: "approve" | "disapprove" = "approve", onSuccess?: (id: string) => void) => {
    let color = type == "disapprove" ? "red" : "lightgreen";
    let emailSentMsg = "An email will be sent to the uploader to notify them of the change.";
    let DEFAULT_DISAPPROVAL_REASON = "Disapproved...";

    let baseProps: any = {
      html: `
        Are you sure you want to <span style='color:${color};font-weight:bold'>${type}</span> this?
        <br><br>
        <div><b>ID</b>: ${item.id}</div>
        <div><b>Title</b>: ${item.title}</div>
        <div><b>Uploader</b>: ${item.creatorUsername}</div>
      `,
      showCancelButton: true,
      showCloseButton: true,
      icon: "warning",
      confirmButtonText: "Yes!"
    };

    if (type == "approve") {
      baseProps.showLoaderOnConfirm = true;
      baseProps.preConfirm = () => {
        return approveSheetMusic(item.id);
      };
    }

    Swal
      .fire({
        ...baseProps
      })
      .then((result) => {
        if (!result.isConfirmed) return;

        if (type == "approve") {
          SwalPR(sfxService).fire({
            icon: "success",
            html: `<b>${item.title}</b> has been approved!<br><br>${emailSentMsg}`
          });
          refreshRepoView();
          if (onSuccess) onSuccess(item.id);
        } else if (type == "disapprove") {
          let disapprovalReason = SheetMusicDisapprovalReasons.Unknown;
          let props: any = { input: "select" };

          props.inputValidator = (_: any) => {
            return Promise.resolve("");
          };

          props.preConfirm = (input: any) => {
            disapprovalReason = input ?? SheetMusicDisapprovalReasons.Unknown;
          };

          Swal
            .fire({
              ...props,
              title: "Select a reason",
              inputPlaceholder: "Select a disapproval reason...",
              inputValue: SheetMusicDisapprovalReasons.Unknown,
              inputOptions: {
                InvalidScoreDetails: SheetMusicDisapprovalReasons.InvalidScoreDetails,
                InvalidFileFormat: SheetMusicDisapprovalReasons.InvalidFileFormat,
                EditChange: SheetMusicDisapprovalReasons.EditChange,
                Unknown: SheetMusicDisapprovalReasons.Unknown
              },
              inputValidator: (input) => {
                return new Promise((resolve) => {
                  if (!input) return resolve("You need to select a reason!");
                  resolve(null);
                });
              },
              showCancelButton: true,
              allowOutsideClick: false,
              allowEscapeKey: true
            })
            .then((result) => {
              if (!result.isConfirmed) return;

              SwalPR(sfxService).fire({
                title: "Reason Details",
                input: "textarea",
                inputAttributes: {
                  'aria-label': 'Type some details of your reasoning here...',
                  "max": "1000"
                },
                inputValue: DEFAULT_DISAPPROVAL_REASON,
                inputPlaceholder: "Type some details of your reasoning here...",
                showCancelButton: true,
                inputAutoTrim: true,
                showLoaderOnConfirm: true,
                allowOutsideClick: false,
                allowEscapeKey: true,
                inputValidator: (input) => {
                  return new Promise((resolve) => {
                    if (!input) return resolve("You need to enter a description!");
                    resolve(null);
                  });
                },
                preConfirm: (input: string) => disapproveSheetMusic(item.id, {
                  reason: disapprovalReason,
                  reasonDetails: input.trim()
                })
              }).then((result) => {
                if (result.isConfirmed) {
                  SwalPR(sfxService).fire({
                    icon: "success",
                    html: `<b>${item.title}</b> has been disapproved!<br><br>${emailSentMsg}`
                  });
                  refreshRepoView();
                  if (onSuccess) onSuccess(item.id);
                }
              }).catch((err: any) => {
                let message = `Something went wrong.`;

                // if (err instanceof AxiosError) {
                //   if (err.response?.data?.error == "already_disapproved") {
                //     message = `This sheet music has already been disapproved.`;
                //   }
                // }

                SwalPR(sfxService).fire({
                  icon: "error",
                  title: `Failed to disapprove: ${item.title}`,
                  html: message
                });
              });
            });
        }
      })
      .catch((err: any) => {
        let message = `Oof! Something went wrong. Please check the console logs.`;

        // if (err instanceof AxiosError) {
        //   if (err.response?.data?.error == "already_approved") {
        //     message = `This sheet music has already been approved.`;
        //   }
        // }

        SwalPR(sfxService).fire({
          icon: "error",
          title: `Failed to ${type}.`,
          html: message
        });
      });
  };

  const searchRepoByTerm = (term: string) => {
    searchDriver()?.getActions().setSearchTerm(term, { shouldClearFilters: false });
  };

  const searchRepoByFilter = (name: string, value: string) => {
    searchDriver()?.getActions().setFilter(name, value);
  };

  const refreshRepoView = () => {
    let currentState = searchDriver()?.getState();
    searchDriver()?.getActions().setSearchTerm(currentState?.searchTerm || "", {
      refresh: true,
      shouldClearFilters: false
    });
    searchDriver()?.getActions().setCurrent(currentState?.current || 0);
  };

  const onDisconnect = () => {
    setRepoActive(false);
    setEditMode(false);
    setNewSheetFromViewerUploadData();
    setSearchDriver();
  };

  const copySheetMusicTextToClipboard = (sheetText?: string) => {
    if (!sheetText) return;

    // Copy text to clipboard
    navigator.clipboard.writeText(sheetText).then(() => {
      toast.success("Sheet copied to clipboard!", {
        duration: 3000
      });
    }).catch(() => {
      toast.error("Failed to copy sheet to clipboard!", {
        duration: 3000
      });
    });
  };

  return {
    loadSheetMusic,
    editMode,
    copySheetMusicTextToClipboard,
    onDisconnect,
    deleteSheetMusic,
    searchRepoByFilter,
    refreshRepoView,
    searchRepoByTerm,
    setRepoActive,
    setSearchDriver,
    setActiveViewerData,
    getSheetMusicDetails,
    startApprovalProcess,
    deleteSheetMusicDialogue,
    activeViewerData,
    setNewSheetFromViewerUploadData,
    newSheetFromViewerUploadData,
    approveSheetMusic,
    disapproveSheetMusic,
    uploadSheetMusic,
    updateSheetMusic,
    setEditMode,
    sheetMusicAddFavorite,
    sheetMusicRemoveFavorite,
    sheetMusicIsFavorited,
    activeDetailID, setActiveDetailID,
    clearActiveSheetMusicViewerData
  };
}