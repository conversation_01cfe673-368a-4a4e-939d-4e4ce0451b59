import { createEventBus, EventBus } from "@solid-primitives/event-bus";
import { makePersisted } from "@solid-primitives/storage";
import clone from 'lodash-es/clone';
import cloneDeep from 'lodash-es/cloneDeep';
import debounce from 'lodash-es/debounce';
import differenceWith from 'lodash-es/differenceWith';
import fromPairs from 'lodash-es/fromPairs';
import isEqual from 'lodash-es/isEqual';
import mapValues from 'lodash-es/mapValues';
import pick from 'lodash-es/pick';
import { createEffect, createSignal, on } from 'solid-js';
import { createStore, reconcile, unwrap } from 'solid-js/store';
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppSettings as ProtoAppSettings } from "~/proto/pianorhythm-app-renditions";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { CoreService } from "~/types/app.types";
import { AppSettings, DefaultAppSettings } from '~/types/settings.types';
import { COMMON } from '~/util/const.common';
import { encodeForProto } from "~/util/helpers";
import { logError } from '~/util/logger';

type AppStorage = {
  settings: string;
  "guest-settings": string;
  rememberMeChecked: string;
  lastSavedUsername: string;
  lastSavedLoggedInUsername: string;
  lastRoomNameEnteredInSearchBar: string;
  lastSavedRoomSettings: string;
  lastSavedAudioDevice: string;
  lastSavedVersion: string;
  lastSavedVersionDate: string;
  downloadDesktopAppReminder: string;
  newFeaturesReminder_SheetMusic: string;
  bgMusicMuted: string;
  useWebGpu: string;
  sceneMode: string;
  useOffscreenCanvas: string;
  offlineMode: string;
};

/**
 * Service for managing application settings.
 *
 * @remarks
 * This service provides functions for setting, getting, and persisting application settings.
 * It also includes functions for initializing the service, loading settings from the server, and resetting settings to default values.
 *
 * @returns An object containing various functions for managing application settings.
 */
export default function AppSettingsService() {
  const [guestAppSettings, setGuestAppSettings] = makePersisted(createStore(cloneDeep(DefaultAppSettings)), { name: "guest-settings" });
  const [appSettings, setAppSettings] = makePersisted(createStore(cloneDeep(DefaultAppSettings)), { name: "app-settings" });
  const [settingSaved, setSettingSaved] = createSignal(1);
  const [initialized, setInitialized] = createSignal(false);
  const [isClientGuest, setIsClientGuest] = createSignal(true);
  const persistSettingsEvent = createEventBus<AppSettings>();
  const settingsUpdatedEvents = createEventBus<AppSettings>();

  createEffect(on(settingSaved, debounce(() =>
    settingsUpdatedEvents.emit(unwrap(appSettings))
  )));

  /**
   * Sets a value in the local storage using the specified key.
   *
   * @param key - The key to use for storing the value.
   * @param value - The value to be stored.
   */
  function setLocalStorage(key: keyof AppStorage, value: any) {
    if (!key) return;
    if (!("localStorage" in self)) return;

    try {
      if (value == undefined) return localStorage.removeItem(key);
      localStorage.setItem(key, JSON.stringify(value));
    } catch (ex) {
      logError(`[setLocalStorage] Failed to set item. Key: ${key} | Value: ${value} | Error: ${ex}`);
    }
  }

  /**
   * Sanitizes the input AppSettings object by removing any null values and ensuring that only the keys present in the DefaultAppSettings object are used.
   *
   * @param input - The input AppSettings object to be sanitized.
   * @returns The sanitized AppSettings object.
   */
  function sanitizeSettings(input: AppSettings) {
    if (!input) return input;
    if (!("localStorage" in self)) return input;

    try {
      let _input = mapValues(unwrap(input), (value) => (value === null ? undefined : value));
      let edited = differenceWith([DefaultAppSettings], [_input], isEqual);
      let actualUsedKeys = Object.keys(edited?.[0] ?? "");
      return { ...DefaultAppSettings, ...pick(_input, actualUsedKeys) } as AppSettings;
    } catch {
      return input;
    }
  }

  async function initialize(coreService: CoreService, appStateEffects: EventBus<AppStateEffects>) {
    if (initialized()) return;

    setInitialized(true);
    persistSettings();

    appStateEffects.listen((effect) => {
      switch (effect.action) {
        case AppStateEffects_Action.AppSettingsUpdated: {
          if (!effect.appSettings) return;
          let appSettings = effect.appSettings;

          // Graphics settings
          setAppSettings("GRAPHICS_PRESET", appSettings.graphicsPreset);
          setAppSettings("GRAPHICS_ENABLE_MOTION_BLUR", appSettings.graphicsEnableMotionBlur);
          setAppSettings("GRAPHICS_ENABLE_ALL_PARTICLES", appSettings.graphicsEnableAllParticles);
          setAppSettings("GRAPHICS_ENABLE_FOG", appSettings.graphicsEnableFog);
          setAppSettings("GRAPHICS_ENABLE_GLOW", appSettings.graphicsEnableGlow);
          setAppSettings("GRAPHICS_ENABLE_AVATARS", appSettings.graphicsEnableAvatars);
          setAppSettings("GRAPHICS_ENABLE_ANTIALIAS", appSettings.graphicsEnableAntialias);
          setAppSettings("GRAPHICS_ENABLE_SHADOWS", appSettings.graphicsEnableShadows);
          setAppSettings("GRAPHICS_ENABLE_PHYSICS", appSettings.graphicsEnablePhysics);
          setAppSettings("GRAPHICS_ENABLE_AMBIENT_OCCLUSION", appSettings.graphicsEnableAmbientOcclusion);
          setAppSettings("GRAPHICS_ENABLE_DEPTH_OF_FIELD", appSettings.graphicsEnableDepthOfField);
          setAppSettings("GRAPHICS_ENABLE_BLOOM", appSettings.graphicsEnableBloom);
          setAppSettings("GRAPHICS_ENABLE_TONE_MAPPING", appSettings.graphicsEnableToneMapping);
          setAppSettings("GRAPHICS_ENABLE_HDR", appSettings.graphicsEnableHdr);
          setAppSettings("GRAPHICS_MSAA_SAMPLES", appSettings.graphicsMsaaSamples);
          setAppSettings("GRAPHICS_SHADOW_FILTER", appSettings.graphicsShadowFilter);
          setAppSettings("GRAPHICS_ENABLE_ANIMATIONS", appSettings.graphicsEnableAnimations);
          setAppSettings("GRAPHICS_ENABLE_LIGHTS", appSettings.graphicsEnableLights);
          setAppSettings("GRAPHICS_USE_LOW_POLY_MODELS", appSettings.graphicsUseLowPolyModels);
          setAppSettings("GRAPHICS_ENABLE_GUITARS", appSettings.graphicsEnableGuitars);
          break;
        }
      }
    });

    settingsUpdatedEvents.listen((settings) => {
      coreService.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetAppSettings,
        appSettings: ProtoAppSettings.fromJSON(encodeForProto(sanitizeSettings(settings)))
      }));
    });
  }

  /**
   * Handles the disconnect event.
   * Clears the member settings.
   */
  function onDisconnect() {
    clearMemberSettings();
  }

  /**
   * Retrieves the application settings.
   *
   * @returns The application settings.
   */
  const getSettings = () => appSettings;

  /**
   * Debounces the persistSettings function and handles saving of app settings.
   *
   * @remarks
   * This function checks if the app is initialized and if the app settings are valid before saving them.
   * If the user is a guest, the guest app settings are saved. Otherwise, the member app settings are saved.
   *
   * @throws Error if there is an error while saving the settings.
   */
  const persistSettings = debounce(() => {
    try {
      if (!initialized() || !appSettings) return;
      let unwrappedSettings = unwrap(appSettings);
      let output = sanitizeSettings(unwrappedSettings);
      if (!output) return;

      if (isClientGuest()) {
        if (COMMON.IS_DEV_MODE) console.debug("Saving guest settings", output);
        setGuestAppSettings(cloneDeep(output));
      } else {
        if (COMMON.IS_DEV_MODE) console.debug("Saving member settings", output);
        persistSettingsEvent.emit(output);
      }
      setSettingSaved(v => v * -1);
    } catch (ex) {
      logError(`[persistSettings] Failed to save settings. Error: ${ex}`);
    }
  });

  /**
   * Retrieves the value of a specific setting from the AppSettings object.
   *
   * @template T - The type of the setting value.
   * @param {keyof AppSettings} setting - The key of the setting to retrieve.
   * @param {boolean} [useDefaultIfMissing=true] - Indicates whether to use the default value if the setting is missing.
   * @returns {T} - The value of the setting.
   */
  function getSetting<T>(setting: keyof AppSettings, useDefaultIfMissing = true): T {
    if (!setting) return null as T;
    let foundSetting = appSettings[setting] as any;
    if (foundSetting == null && useDefaultIfMissing) return DefaultAppSettings[setting] as any;
    return foundSetting;
  }

  /**
   * Saves a setting with the specified value.
   *
   * @param setting - The key of the setting to save.
   * @param value - The value to save for the setting.
   * @param persist - Optional. Specifies whether to persist the setting. Default is true.
   * @param preventRefresh - Optional. Specifies whether to prevent refreshing. Default is false.
   */
  function saveSetting(setting: keyof AppSettings, value: any) {
    try {
      setAppSettings(setting, value);
      persistSettings();
    } catch (ex) {
      logError(`[saveSetting] Failed to save setting. Key: ${setting} | Value: ${value} | Error: ${ex}`);
    }
  }

  /**
   * Retrieves a value from local storage based on the provided key.
   *
   * @template T - The type of the value to retrieve.
   * @param key - The key of the value in local storage.
   * @param defaultValue - The default value to return if the value is not found or cannot be parsed.
   * @returns The retrieved value from local storage, or the default value if not found or cannot be parsed.
   */
  function getLocalStorage<T>(key: keyof AppStorage, defaultValue?: T) {
    try {
      let value = JSON.parse(localStorage.getItem(key) ?? "null") as T;
      return value;
    } catch (ex) {
      logError(`[getLocalStorage] Failed to parse item. Key: ${key} | Error: ${ex}`);
      return defaultValue || undefined;
    }
  }

  /**
   * Loads the settings from the server and updates the application settings.
   *
   * @param clientSettings - The client settings received from the server.
   */
  function loadSettingsFromServer(clientSettings: AppSettings) {
    if (!clientSettings) return;
    setAppSettings(sanitizeSettings(clientSettings));
  }

  /**
   * Loads the guest settings from local storage and sets them as the application settings.
   * If the user is not a guest, the function does nothing.
   * If the guest settings exist in local storage and the guestAppSettings variable is defined,
   * the function sanitizes the settings and sets them as the application settings.
   * If the application is in development mode, a debug message is logged with the loaded app settings.
   * If an error occurs while parsing the settings, an error message is logged.
   */
  function loadGuestSettings() {
    try {
      if (!isClientGuest()) return;
      if (localStorage.getItem("guest-settings") && guestAppSettings) setAppSettings(sanitizeSettings(guestAppSettings));
      if (COMMON.IS_DEV_MODE) console.debug("Loaded guest settings", unwrap(appSettings));
    } catch (ex) {
      logError(`[loadGuestSettings] Failed to parse settings. Error: ${ex}`);
    }
  }

  function clearMemberSettings() {
    if (!("localStorage" in self)) return;
    // removeLocalStorageSetting("settings");
  }

  function clearGuestSettings() {
    if (!("localStorage" in self)) return;
    // removeLocalStorageSetting("guest-settings");
  }

  function clearSettings() {
    if (isClientGuest()) {
      clearGuestSettings();
    } else {
      clearMemberSettings();
    }
  }

  /**
   * Resets the settings to their default values.
   */
  function resetSettingsToDefault() {
    clearSettings();
    setAppSettings(clone(DefaultAppSettings));
    persistSettings();
    setSettingSaved(v => v * -1);
  }

  /**
   * Resets the graphics settings to their default values.
   * This function clears the current settings, retrieves the default settings,
   * filters out the graphics-related settings, and updates the app settings accordingly.
   * After updating the settings, it persists them and triggers a setting saved event.
   */
  function resetGraphicsSettingsToDefault() {
    clearSettings();
    let defaultSettings = clone(DefaultAppSettings);
    let graphicsSettings = Object.entries(defaultSettings).filter((([key]) => key.indexOf("GRAPHICS_") != -1));
    setAppSettings(reconcile({
      ...appSettings,
      ...fromPairs(graphicsSettings),
      "WEBGL_POWER_PREFERENCE": defaultSettings.WEBGL_POWER_PREFERENCE
    }));

    persistSettings();
    setSettingSaved(v => v * -1);
  }

  function resetSoundFxSettingsToDefault() {
    clearSettings();

    let defaultSettings = clone(DefaultAppSettings);
    let targetSettings = Object.entries(defaultSettings).filter((([key]) => key.indexOf("AUDIO_SFX_") != -1));
    setAppSettings(reconcile({
      ...appSettings,
      ...fromPairs(targetSettings)
    }));

    persistSettings();
    setSettingSaved(v => v * -1);
  }

  function resetAudioSoundfontSettingsToDefault() {
    clearSettings();

    let defaultSettings = clone(DefaultAppSettings);
    let applicableKeys = ([
      "AUDIO_SAMPLE_RATE",
      "AUDIO_CHANNEL_INTERPOLATION_METHOD",
      "AUDIO_ENABLE_DRUM_CHANNEL",
      "AUDIO_MIDI_OUTPUT_ONLY",
      "AUDIO_OUTPUT_OWN_NOTES_TO_MIDI",
      "AUDIO_MOUSE_POS_SETS_VELOCITY",
      "AUDIO_MAX_POLYPHONY",
      "AUDIO_MAX_NOTE_ON_TIME",
      "AUDIO_ENABLE_REVERB",
      "DESKTOP_SAVE_SOUNDFONTS",
      "AUDIO_SYNTH_ENGINE",
      "AUDIO_EQUALIZER_PRESET",
      "AUDIO_EQUALIZER_BANDS",
      "AUDIO_CACHE_SOUNDFONTS_WEB"
    ] as (keyof AppSettings)[]).map(x => x.toLowerCase());

    let targetSettings = Object.entries(defaultSettings).filter((([key]) => applicableKeys.includes(key.toLowerCase())));
    setAppSettings(reconcile({
      ...appSettings,
      ...fromPairs(targetSettings)
    }));

    persistSettings();
    setSettingSaved(v => v * -1);
  }

  const isDebugMode = () =>
    ("localStorage" in self && JSON.parse(localStorage.getItem("debugMode") ?? "false")) ||
    COMMON.IS_DEV_MODE || COMMON.IS_STAGING || Boolean((settingSaved() && getSetting<boolean>("ENABLE_DEBUG_MODE")));

  return {
    setLocalStorage,
    getSettings,
    persistSettings,
    getSetting,
    saveSetting,
    getLocalStorage,
    loadSettingsFromServer,
    loadGuestSettings,
    clearMemberSettings,
    clearGuestSettings,
    clearSettings,
    resetSettingsToDefault,
    resetGraphicsSettingsToDefault,
    resetSoundFxSettingsToDefault,
    resetAudioSoundfontSettingsToDefault,
    isDebugMode,
    initialize,
    onDisconnect,
    isClientGuest,
    setIsClientGuest,
    settingSaved,
    settingsUpdatedEvents,
    persistSettingsEvent
  };
}