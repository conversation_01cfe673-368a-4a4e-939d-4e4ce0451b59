import { makeCache } from "@solid-primitives/resource";
import { memoize } from "lodash-es";
import { Accessor, createResource } from "solid-js";
import { getAssetImage, getUserProfileImage } from "~/server/general.api";
import { IMAGES } from "~/util/const.common";
import { getInstrumentImageFromName } from "~/util/helpers.instruments";

/**
 * ResourceService is a service that handles resource management.
 * It provides methods for retrieving and caching various types of assets.
 *
 * @returns An object with methods for managing resources.
 */
export default function ResourceService() {
  const [_defaultProfileImage] = makeCache(() => getAssetImage(IMAGES.DEFAULT_PROFILE_IMAGE, true), {
    storage: localStorage,
    storageKey: "default-profile-image-cache",
    expires: 1000 * 60 * 60 * 24 * 7
  });
  const [defaultProfileImage] = createResource(_defaultProfileImage);

  const [_defaultHelpBotProfileImage] = makeCache(() => getAssetImage(IMAGES.DEFAULT_HELP_BOT_PROFILE_IMAGE, true), {
    storage: localStorage,
    storageKey: "default-helpbot-profile-image-cache",
    expires: 1000 * 60 * 60 * 24 * 7
  });
  const [defaultHelpBotProfileImage] = createResource(_defaultHelpBotProfileImage);

  const [_crownImage] = makeCache(() => getAssetImage("/images/crown.png"), {
    storage: localStorage,
    storageKey: "crown-image-cache",
    expires: 1000 * 60 * 60 * 24 * 7
  });
  const [crownImage] = createResource(_crownImage);

  const [_instrumentImageFetch] = makeCache((instrumentName: string) =>
      getAssetImage(`/instruments/${instrumentName}.png`),
    {
      storage: localStorage,
      storageKey: "instrument-image-cache",
      expires: 1000 * 60 * 60 * 24 * 7
    });

  /**
   * Fetches the instrument image based on the provided instrument name.
   *
   * @param instrumentName - The accessor function that returns the instrument name.
   * @returns A resource containing the instrument image fetched from the provided instrument name.
   */
  const instrumentImageFetch = (instrumentName: Accessor<string | undefined>) => {
    return createResource(() => getInstrumentImageFromName(instrumentName()), _instrumentImageFetch);
  };

  const initialize = async () => {
  };

  /**
   * Retrieves an asset image.
   *
   * @param asset - The asset to retrieve the image for.
   * @param _disableCache - Optional parameter to disable caching. Default is false.
   * @returns A promise that resolves to the asset image.
   */
  const _getAssetImage = async (asset: string, _disableCache = false) => {
    return getAssetImage(asset);
  };

  const _getUserProfileImage = memoize(getUserProfileImage);

  return ({
    initialize,
    defaultProfileImage,
    defaultHelpBotProfileImage,
    getAssetImage: _getAssetImage,
    crownImage,
    instrumentImageFetch,
    getServerProfileImage: (usertag: string, profileImageLastModified?: string | Date) => {
      return _getUserProfileImage(usertag, profileImageLastModified);
    },
    getServerImage: async (url: string, _disableCache = false) => {
      // console.log("getServerImage", url);
      return url;
    },
    getDefaultProfileImage: async () => {
      return defaultProfileImage();
    },
    getDefaultHelpBotProfileImage: async () => {
      return defaultHelpBotProfileImage();
    }
  });
}