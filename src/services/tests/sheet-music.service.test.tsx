import { MockDisplaysService } from '@test/mocks/service.mocks';
import { useService } from 'solid-services';
import Swal from 'sweetalert2';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { getSheetMusicDetailsQuery } from '~/lib/sheet_music';
import { SheetMusicDisapprovalReasons } from '~/models/sheet-music-dbo.models';
import { sheetMusicUpsert, sheetMusicApprove, sheetMusicDisapprove, sheetMusicDelete } from '~/server/sheet-music.api';
import SwalPR from '~/util/sweetalert';
import DisplaysService from '../displays.service';
import { SheetMusicService } from '../sheet-music.service';
import SoundEffectsService from '../sound-effects.service';

// src/services/sheet-music.service.test.ts

vi.mock('solid-services', () => ({
  useService: vi.fn((Service) => {
    if (Service === DisplaysService) {
      return () => ({
        setDisplay: vi.fn()
      });
    }
    if (Service === SoundEffectsService) {
      return () => ({});
    }
    return () => ({});
  })
}));

vi.mock('~/lib/sheet_music', () => ({
  getSheetMusicDetailsQuery: vi.fn()
}));

vi.mock('~/server/sheet-music.api', () => ({
  sheetMusicUpsert: vi.fn(),
  sheetMusicApprove: vi.fn(),
  sheetMusicDisapprove: vi.fn(),
  sheetMusicDelete: vi.fn()
}));

vi.mock('solid-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

vi.mock('sweetalert2', () => ({
  default: {
    fire: vi.fn(),
    clickConfirm: vi.fn(),
  }
}));

vi.mock('~/util/sweetalert', () => ({
  default: vi.fn(() => ({
    fire: vi.fn().mockReturnValue(Promise.resolve({ isConfirmed: true }))
  }))
}));

// Mock global fetch
const mockFetchResponse = (data: any) => Promise.resolve({
  json: () => Promise.resolve(data)
});

global.fetch = vi.fn();
Object.defineProperty(global, 'navigator', {
  value: {
    clipboard: {
      writeText: vi.fn()
    }
  },
  writable: true
});

describe('SheetMusicService', () => {
  let service: ReturnType<typeof SheetMusicService>;
  let displaysService: ReturnType<typeof DisplaysService>;

  beforeEach(() => {
    vi.clearAllMocks();

    displaysService = MockDisplaysService();
    (useService as any).mockImplementation((service: any) => {
      if (service === DisplaysService) return () => displaysService;
    });

    service = SheetMusicService();
  });

  describe('loadSheetMusic', () => {
    it('should load sheet music and set display when details are found', async () => {
      const mockDetails = { id: '123', title: 'Test Sheet' };
      (getSheetMusicDetailsQuery as any as Mock).mockResolvedValue(mockDetails);

      await service.loadSheetMusic('123');

      expect(getSheetMusicDetailsQuery).toHaveBeenCalledWith('123');
      // Cannot directly test setActiveViewerData as it's a mock, but we can verify that setDisplay was called
      expect(displaysService.setDisplay).toHaveBeenCalledWith('SHEET_MUSIC_VIEWER', true);
    });

    it('should not set display when no details are found', async () => {
      (getSheetMusicDetailsQuery as any as Mock).mockResolvedValue(null);

      await service.loadSheetMusic('123');

      expect(getSheetMusicDetailsQuery).toHaveBeenCalledWith('123');
      expect(displaysService.setDisplay).not.toHaveBeenCalled();
    });
  });

  describe('uploadSheetMusic', () => {
    it('should reject if no data is provided', async () => {
      await expect(service.uploadSheetMusic(null!)).rejects.toEqual(
        'Failed to upload. No Sheet data was provided.'
      );
    });

    it('should call sheetMusicUpsertBase with correct parameters', async () => {
      const mockData = { title: 'Test Sheet' };
      const mockResponse = { data: { id: '123' } };
      (sheetMusicUpsert as any as Mock).mockResolvedValue(mockResponse);

      await service.uploadSheetMusic(mockData);

      expect(sheetMusicUpsert).toHaveBeenCalledWith(mockData, 'create', 'POST');
    });
  });

  describe('updateSheetMusic', () => {
    it('should reject if no id is provided', async () => {
      await expect(service.updateSheetMusic({} as any, undefined)).rejects.toEqual(
        'Failed to update. No Sheet ID was provided.'
      );
    });

    it('should call sheetMusicUpsertBase with correct parameters', async () => {
      const mockData = { title: 'Test Sheet' };
      const mockResponse = { data: { id: '123' } };
      (sheetMusicUpsert as any as Mock).mockResolvedValue(mockResponse);

      await service.updateSheetMusic(mockData, '123');

      expect(sheetMusicUpsert).toHaveBeenCalledWith(mockData, 'update/123', 'PATCH');
    });
  });

  describe('approval functions', () => {
    it('should call the correct API for approveSheetMusic', async () => {
      const mockResponse = { data: { id: '123' } };
      (sheetMusicApprove as any as Mock).mockResolvedValue(mockResponse);

      await service.approveSheetMusic('123');

      expect(sheetMusicApprove).toHaveBeenCalledWith('123');
    });

    it('should call the correct API for disapproveSheetMusic', async () => {
      const request = {
        reason: SheetMusicDisapprovalReasons.InvalidFileFormat,
        reasonDetails: 'Test reason'
      };
      const mockResponse = { data: { id: '123' } };
      (sheetMusicDisapprove as any as Mock).mockResolvedValue(mockResponse);

      await service.disapproveSheetMusic('123', request);

      expect(sheetMusicDisapprove).toHaveBeenCalledWith('123', request);
    });
  });

  describe('deleteSheetMusic', () => {
    it('should call the correct API', async () => {
      const mockResponse = { data: { id: '123' } };
      (sheetMusicDelete as any as Mock).mockResolvedValue(mockResponse);

      await service.deleteSheetMusic('123');

      expect(sheetMusicDelete).toHaveBeenCalledWith('123');
    });
  });

  describe('favorite functions', () => {
    it('should return false for sheetMusicIsFavorited if no id or usertag', async () => {
      const result = await service.sheetMusicIsFavorited('', '');
      expect(result).toBe(false);
    });

    it('should call correct API for sheetMusicAddFavorite', async () => {
      (global.fetch as any as Mock).mockResolvedValue(mockFetchResponse({ success: true }));

      await service.sheetMusicAddFavorite('123', 'user1');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/sheet_music/favorite/add'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ sheetMusicID: '123', usertag: 'user1' })
        })
      );
    });

    it('should call correct API for sheetMusicRemoveFavorite', async () => {
      (global.fetch as any as Mock).mockResolvedValue(mockFetchResponse({ success: true }));

      await service.sheetMusicRemoveFavorite('123', 'user1');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/sheet_music/favorite/remove'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ sheetMusicID: '123', usertag: 'user1' })
        })
      );
    });
  });

  describe('copySheetMusicTextToClipboard', () => {
    it('should not call (navigator.clipboard.writeText as any as Mock) if no text provided', () => {
      service.copySheetMusicTextToClipboard(undefined);
      expect((navigator.clipboard.writeText as any as Mock)).not.toHaveBeenCalled();
    });

    it('should copy text to clipboard', async () => {
      (navigator.clipboard.writeText as any as Mock).mockResolvedValue(undefined);

      service.copySheetMusicTextToClipboard('test sheet');

      expect((navigator.clipboard.writeText as any as Mock)).toHaveBeenCalledWith('test sheet');
    });

    it('should show error message when clipboard write fails', async () => {
      (navigator.clipboard.writeText as any as Mock).mockRejectedValue(new Error('Clipboard error'));

      service.copySheetMusicTextToClipboard('test sheet');

      expect((navigator.clipboard.writeText as any as Mock)).toHaveBeenCalledWith('test sheet');
    });
  });

  describe('dialog interactions', () => {
    it.skip('should properly handle deleteSheetMusicDialogue confirmation', async () => {
      const mockItem = { id: '123', title: 'Test Sheet', creatorUsername: 'user1' };
      const onSuccessMock = vi.fn();

      service.deleteSheetMusicDialogue(mockItem, onSuccessMock);

      expect(SwalPR).toHaveBeenCalled();
      console.log("SwalPR()", Swal.clickConfirm());
      // Trigger Swal confirmation

      // expect(service.deleteSheetMusic).toHaveBeenCalledWith('123');
      expect(onSuccessMock).toHaveBeenCalledWith('123');
    });

    it('should call startApprovalProcess with correct parameters', async () => {
      (Swal.fire as any as Mock).mockResolvedValue({ isConfirmed: true });
      const mockItem = { id: '123', title: 'Test Sheet', creatorUsername: 'user1' };

      service.startApprovalProcess(mockItem as any, 'approve');

      expect(Swal.fire).toHaveBeenCalledWith(
        expect.objectContaining({
          showCancelButton: true,
          icon: 'warning'
        })
      );
    });
  });

  describe('search repository functions', () => {
    it('should call searchDriver methods for searchRepoByTerm', () => {
      const mockSearchDriver = {
        getActions: vi.fn().mockReturnValue({
          setSearchTerm: vi.fn()
        })
      };
      service.setSearchDriver(mockSearchDriver as any);

      service.searchRepoByTerm('test');

      expect(mockSearchDriver.getActions().setSearchTerm).toHaveBeenCalledWith(
        'test',
        { shouldClearFilters: false }
      );
    });

    it('should call searchDriver methods for searchRepoByFilter', () => {
      const mockSearchDriver = {
        getActions: vi.fn().mockReturnValue({
          setFilter: vi.fn()
        })
      };
      service.setSearchDriver(mockSearchDriver as any);

      service.searchRepoByFilter('category', 'value');

      expect(mockSearchDriver.getActions().setFilter).toHaveBeenCalledWith(
        'category',
        'value'
      );
    });
  });

  describe('onDisconnect', () => {
    it.skip('should reset service state', () => {
      const mockSetRepoActive = vi.fn();
      const mockSetEditMode = vi.fn();
      const mockSetNewSheetFromViewerUploadData = vi.fn();
      const mockSetSearchDriver = vi.fn();

      // Replace the mock implementations for this test
      service.setRepoActive = mockSetRepoActive;
      service.setEditMode = mockSetEditMode;
      service.setNewSheetFromViewerUploadData = mockSetNewSheetFromViewerUploadData;
      service.setSearchDriver = mockSetSearchDriver;

      service.onDisconnect();

      expect(mockSetRepoActive).toHaveBeenCalledWith(false);
      expect(mockSetEditMode).toHaveBeenCalledWith(false);
      expect(mockSetNewSheetFromViewerUploadData).toHaveBeenCalled();
      expect(mockSetSearchDriver).toHaveBeenCalled();
    });
  });
});