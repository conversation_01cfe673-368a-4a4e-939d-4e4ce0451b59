import { <PERSON>, HStack, Spinner, Text, VStack } from "@hope-ui/solid";
import isString from "lodash-es/isString";
import { FaSolidCircleCheck, FaSolidCircleExclamation, FaSolidCircleInfo, FaSolidCircleXmark, FaSolidTriangleExclamation } from "solid-icons/fa";
import { Component, JSXElement, Show } from "solid-js";
import { Motion } from "solid-motionone";
import toast, { Toast, ToastOptions } from "solid-toast";
import { sanitizeTextForNotifications } from "~/util/sanitizeText";

const style = {
  style: {
    "min-width": '300px',
    padding: "5px",
    "font-size": "0.9em",
    background: 'var(--hope-colors-primaryAlpha2)',
    color: 'var(--hope-colors-neutral12)',
    border: '2px solid var(--hope-colors-neutral8)',
  },
  iconTheme: {
    primary: 'var(--hope-colors-success10)',
    secondary: '#1f2937'
  }
};

/**
 * Options for the NotificationService.
 *
 * @typedef {Object} NotificationServiceOptions
 * @property {("success" | "warning" | "danger" | "info")} [type] - The type of the notification.
 * @property {string} [title] - The title of the notification.
 * @property {(string | JSXElement)} description - The description of the notification.
 * @property {boolean} [loading] - Indicates if the notification is in a loading state.
 * @property {boolean} [closable] - Indicates if the notification can be closed.
 * @property {boolean} [persistent] - Indicates if the notification should persist even after being closed.
 */
export type NotificationServiceOptions = {
  type?: "success" | "warning" | "danger" | "info";
  title?: string;
  description: string | JSXElement;
  loading?: boolean;
  closable?: boolean;
  persistent?: boolean;
};

const CustomToast: Component<{
  icon?: JSXElement;
  _toast: Toast;
} & NotificationServiceOptions> = (props) => {
  // const [life, setLife] = createSignal(0);
  const t = props._toast;
  // const startTime = Date.now();
  // const duration = t.duration ?? 2000;

  // onMount(() => {
  //   if (t.paused || duration == Infinity) return;
  //   const interval = window.setInterval(() => {
  //     const diff = Date.now() - startTime - t.pauseDuration;
  //     setLife(100 - (diff / duration * 100));
  //   });

  //   onCleanup(() => clearInterval(interval));
  // });

  return (
    <>
      {props.closable && <Box
        position={"absolute"}
        top={"2px"}
        right={"5px"}
        opacity={0.5}
        transition={"opacity 0.2s ease-in-out"}
        _hover={{ cursor: "pointer", opacity: 1 }}
        onClick={() => toast.remove(t.id)}
      >
        <FaSolidCircleXmark font-size={"14px"} />
      </Box>
      }
      <HStack
        data-testid="notification-custom-toast"
        data-toast-type={props.type}
        spacing={"$2_5"}
      >
        <Motion
          style={{
            "padding-right": "5px",
          }}
          initial={{
            transform: "scale(0)",
          }}
          animate={{
            transform: "scale(1)",
          }}
          transition={{
            duration: 0.5,
            delay: 0.2,
          }}
        >
          <Show when={props.loading} fallback={props.icon}>
            <Spinner
              thickness={"$1"}
              color={"$neutral11"}
              emptyColor="$neutral8"
              marginTop={3}
            />
          </Show>
        </Motion>
        <VStack alignItems={"flex-start"}>
          {props.title && <strong style={{ "margin-bottom": "5px" }}>{props.title}</strong>}
          <Show when={isString(props.description)} fallback={props.description}>
            <Text
              color={"$neutral12"}
              innerHTML={sanitizeTextForNotifications(props.description as string)}
            />
          </Show>
        </VStack>
      </HStack>
      {/* <Box
        position={"absolute"}
        bottom={"0"}
        left={"0"}
        h={"2px"}
        background={"$primaryLight"}
        w={`${life()}%`}
      /> */}
    </>
  );
};

const ICON_FONT_SIZE = "20px";

/**
 * NotificationService is responsible for displaying toast notifications.
 */
const NotificationService = {
  hide: toast.remove,
  show: (options: ToastOptions & NotificationServiceOptions) => {
    let { type,
      loading,
      persistent,
      description: message,
      title, ...rest
    } = options;

    const createToast = (icon?: () => JSXElement) => {
      return toast((t) =>
        <CustomToast
          icon={icon?.()}
          _toast={t}
          loading={loading}
          persistent={persistent}
          closable={rest.closable}
          description={message} title={title}
        />, { ...style, ...rest });
    };

    switch (type) {
      case "success":
        return createToast(() => <FaSolidCircleCheck
          font-size={ICON_FONT_SIZE}
          color="var(--hope-colors-success10)"
        />);
      case "warning":
        return createToast(() => <FaSolidTriangleExclamation
          font-size={ICON_FONT_SIZE}
          color="var(--hope-colors-warning10)"
        />);
      case "danger":
        return createToast(() => <FaSolidCircleExclamation
          font-size={ICON_FONT_SIZE}
          color="var(--hope-colors-danger10)" />);
      case "info":
        return createToast(() => <FaSolidCircleInfo
          font-size={ICON_FONT_SIZE}
          color="var(--hope-colors-accent1)"
        />);
      default:
        return createToast();
    }
  },
};

export default NotificationService;