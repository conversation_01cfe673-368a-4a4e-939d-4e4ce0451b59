import { createEventBus } from "@solid-primitives/event-bus";
import { listen, UnlistenFn } from "@tauri-apps/api/event";
import { distinctUntilChanged, Subject, Subscription } from "rxjs";
import { createImmerSignal } from "solid-immer";
import { createEffect, createSignal } from "solid-js";
import { useService } from "solid-services";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppStateEvents } from "~/proto/pianorhythm-events";
import { CreateRoomParam, RoomOwnerCommandDU, ServerCommandDU, ServerMessage, ServerMessageType } from "~/proto/server-message";
import { getApiServerHost } from "~/server/general.api";
import { AppSettings } from "~/types/settings.types";
import { ChatMessageInputData, ChatMessageInputDataOptionsDefault, ServerCommand, ServerModCommand, ServerMsg, UserUpdate, WebSocketDataEvent } from "~/types/websocket.types";
import { COMMON, IDS, USER_INPUT } from "~/util/const.common";
import { logDebug, logError, logInfo } from "~/util/logger";
import { handleServerMessageEncode, parseProtoClientMessage } from "~/util/websocket.util";
import AppService from "./app.service";
import { default as NotificationService, default as notificationService } from "./notification.service";
import AppSettingsService from "./settings-storage.service";
import SoundEffectsService from "./sound-effects.service";

// @ts-ignore
import * as Timesync from 'timesync';
import { match } from "ts-pattern";
import { AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import isString from "lodash-es/isString";

const MAX_RETRY_ATTEMPTS = 7;
const INITIAL_RETRY_DELAY = 5000;
const MAX_RETRY_DELAY = 30_000;
let retryCount = 0;
let retryTimeout: number;

/**
 * Represents a WebSocket service that handles communication with the WebSocket server.
 */
export default function WebsocketService() {
  const appService = useService(AppService);
  const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);

  const [connected, setConnected] = createSignal(false);
  const [initialized, setInitialized] = createSignal(false);
  const [serverTimeOffset, setServerTimeOffset] = createSignal(0);
  const [syncRetries, setSyncRetries] = createSignal(0);
  const [timesync, setTimesync] = createImmerSignal<any>(undefined);

  const isTypingSubject = new Subject<boolean>();
  // TODO: use subscriptions
  const [subscriptions, setSubscriptions] = createImmerSignal<Subscription[]>([]);
  // TODO
  const [unlistenFns, setUnlistenFns] = createImmerSignal<UnlistenFn[]>([]);
  const websocketEvents = createEventBus<"connected" | "closed" | "error">();

  /**
   * Initializes the WebSocket service.
   *
   * This function sets up event listeners and subscriptions for the WebSocket service.
   * It also initializes time synchronization.
   */
  const initialize = () => {
    if (initialized()) return;
    setInitialized(true);

    appSettingsService().persistSettingsEvent.listen(uploadClientSettings);

    appService().appStateEvents.listen(async (event) => {
      switch (event) {
        case AppStateEvents.AppStateReset: {
          disconnect();
          break;
        }
      }
    });

    appService().appStateEffects.listen((effect) => {
      match(effect.action)
        .with(AppStateEffects_Action.OnWelcome, () => {
          sendServerTimeOffset(serverTimeOffset());
        })
        .otherwise(() => { });
    });

    appService().onDisconnectEvents.listen(() => {
      onDisconnect();
    });

    setSubscriptions(subs => {
      subs.push(
        isTypingSubject
          .pipe(distinctUntilChanged())
          .subscribe((value) => postEmitServerCommand(["IsTyping", value]))
      );
    });

    initializeTimeSync();

    if (COMMON.IS_DESKTOP_APP) {
      listen<Array<number>>("websocket_data", (evt) => {
        let data = evt.payload;
        let [, message] = parseProtoClientMessage(Uint8Array.from(data));
        if (message) onHandleWebSocketDataEvent(message);
      });
    }
  };

  const onDisconnect = () => {
    setConnected(false);
    websocketEvents.emit("closed");
    window.clearTimeout(retryTimeout);
    NotificationService.hide("websocket-reconnect");
  };

  function initializeTimeSync() {
    if (timesync() != null || appService().offlineMode()) return;

    // let server = COMMON.IS_DEV_MODE && COMMON.IS_WEB_APP ? "/sync" : `${COMMON.WS_HOST}/sync`;
    let server = `${COMMON.WS_HOST}/sync`;
    if (appSettingsService().isDebugMode()) logDebug(`Setting up timesync. Server: ${server}`);

    let ts = Timesync.create({
      server,
      interval: 1000 * 60 * 2,
      repeat: 3,
    });
    setTimesync(ts);

    ts.on('change', (_offset: number) => {
      setServerTimeOffset(Math.abs(_offset));
      setSyncRetries(0);

      // Update the enhanced timing system in the note buffer engine
      const localTime = Date.now();
      const serverTime = localTime + _offset;
      const pingTime = ts.rtt || 0; // Get RTT from timesync

      appService().coreService()?.update_server_timing(pingTime, serverTime, localTime);

      if (appSettingsService().isDebugMode()) {
        console.log(`Enhanced timing updated - RTT: ${pingTime}ms, Offset: ${_offset}ms`);
      }
    });

    ts.on('error', async (ex: Error) => {
      if (appService().offlineMode()) return;

      if (ex.message.includes("bad HTTP StatusCode")) {
        if (syncRetries() > 3) {
          // disconnect
          // globalService().onDisconnected("Failed to sync with server.");
          websocketEvents.emit("error");
          logError("Timesync error | " + ex);
        }
        setSyncRetries(cur => cur + 1);
      }
    });

    logInfo("Server timesync initialized.");
  }

  /**
   * Sends the server time offset to the core service.
   *
   * @param value - The value of the server time offset.
   */
  const sendServerTimeOffset = (value: number) => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SetServerTimeOffset,
      doubleValue: value
    }));
  };

  createEffect(() => {
    sendServerTimeOffset(serverTimeOffset());
  });

  /**
   * Connects to a WebSocket server using the provided WebSocket identity.
   *
   * @param wsIdentity - The WebSocket identity.
   * @param onComplete - A callback function to be called when the connection is successfully established.
   */
  const connect = async (wsIdentity: string, onComplete: () => void) => {
    const reconnect = async () => {
      if (retryCount >= MAX_RETRY_ATTEMPTS) {
        NotificationService.show({
          id: "websocket-reconnect",
          type: "danger",
          description: `Failed to reconnect after ${MAX_RETRY_ATTEMPTS} attempts. Please refresh the page to try again.`,
          duration: 30_000
        });
        return;
      }

      const delay = Math.min(INITIAL_RETRY_DELAY * Math.pow(2, retryCount), MAX_RETRY_DELAY);
      retryCount++;

      NotificationService.show({
        id: "websocket-reconnect",
        type: "warning",
        description: `Attempting reconnection in ${delay}ms (attempt ${retryCount}/${MAX_RETRY_ATTEMPTS})`,
        duration: 30_000
      });

      window.clearTimeout(retryTimeout);
      retryTimeout = window.setTimeout(async () => {
        try {
          if (COMMON.IS_WEB_APP) {
            await appService().coreService()?.websocket_connect(`${webSocketURL}/${wsIdentity}`, () => {
              retryCount = 0;
              clearTimeout(retryTimeout);
              setConnected(true);
              websocketEvents.emit("connected");
            }, onError, onClose);
          } else if (COMMON.IS_DESKTOP_APP) {
            // await appService().coreService()?.websocket_connect(`${webSocketURL}/${wsIdentity}`);
          }
        } catch (error) {
          console.error('Reconnection failed:', error);
          await reconnect();
        }
      }, delay);
    };

    const onConnect = () => {
      retryCount = 0;
      clearTimeout(retryTimeout);
      onComplete();
      setConnected(true);
      websocketEvents.emit("connected");
    };

    const onError = () => {
      if (appSettingsService().isDebugMode()) console.error("Failed to connect to server");
      setConnected(false);
      websocketEvents.emit("error");
      reconnect();
    };

    const onClose = () => {
      websocketEvents.emit("closed");

      if (appSettingsService().isDebugMode()) console.error("Received close event from websocket server");
      if (!connected()) return;

      NotificationService.hide(IDS.SERVER_DISCONNECT);
      NotificationService.show({
        id: IDS.SERVER_DISCONNECT,
        type: "danger",
        description: "Disconnected from server",
        duration: 5000
      });

      setConnected(false);
      reconnect();
    };

    let apiServer = await getApiServerHost();
    let webSocketURL = `${apiServer.replace("http", "ws").replace("https", "wss")}/api/websocket`;

    if (COMMON.IS_WEB_APP) {
      await appService().coreService()?.websocket_connect(`${webSocketURL}/${wsIdentity}`, onConnect, onError, onClose);
    } else if (COMMON.IS_DESKTOP_APP) {
      appService().coreService()?.websocket_disconnect();
      let sub = appService().appStateEffects.listen((effect) => {
        if (effect.action == AppStateEffects_Action.ConnectionState) {
          if (effect.stringValue?.toLocaleLowerCase() == "online") {
            onConnect();
            sub();
          }
        }
      });
      await appService().coreService()?.websocket_connect(`${webSocketURL}/${wsIdentity}`);
    }
  };

  const onHandleWebSocketDataEvent = (event: WebSocketDataEvent<any>) => {
    console.log("Received message: ", event);
  };

  /**
   * Joins a room by its name.
   *
   * @param roomName - The name of the room to join.
   * @param createRoomIfNotExist - Optional. Specifies whether to create the room if it doesn't exist. Default is false.
   */
  function joinRoomByName(roomName: string, createRoomIfNotExist = false) {
    try {
      appService().coreService()?.send_app_action(
        AppStateActions.create({
          action: AppStateActions_Action.JoinRoomByName,
          joinRoomByNameRequest: { roomName, createRoomIfNotExist }
        }));
    } catch (ex) {
      appService().setActivatePageLoader(false);
      logError("[joinRoomByName] " + ex);
      sfxService().playErrorSFX();
      notificationService.show({
        type: "danger",
        title: `Failed to join room: ${roomName}`,
        description: "Something went wrong when trying to join the room. Please try again later..."
      });
    }
  }

  /**
   * Joins the next available lobby.
   *
   * @remarks
   * This function sends an app action to join the next available lobby in the application.
   * If an error occurs during the process, it displays a notification and plays an error sound.
   *
   * @throws {Error} If an error occurs during the process.
   */
  function joinNextAvailableLobby() {
    try {
      appService().coreService()?.send_app_action(
        AppStateActions.create({ action: AppStateActions_Action.JoinNextAvailableLobby })
      );
    } catch (ex) {
      appService().setActivatePageLoader(false);
      logError("[joinNextAvailableLobby] " + ex);
      sfxService().playErrorSFX();
      notificationService.show({
        type: "danger",
        title: `Failed to join the next available lobby.`,
        description: "Something went wrong when trying to join a lobby. Please try again later..."
      });
    }
  }

  /**
   * Joins a room by its ID.
   *
   * @param roomID - The ID of the room to join.
   * @param password - The password for the room (optional).
   * @returns A string indicating the result of the operation. Possible values are "Success" or "Failed".
   */
  function joinRoomByID(roomID: string, password?: string) {
    try {
      if (password != null) {
        appService().coreService()?.send_app_action(
          AppStateActions.create({
            action: AppStateActions_Action.JoinRoomById, roomIdWithPassword: {
              roomID, password
            }
          })
        );
      } else {
        appService().coreService()?.send_app_action(
          AppStateActions.create({ action: AppStateActions_Action.JoinRoomById, roomId: roomID })
        );
      }

    } catch (ex) {
      appService().setActivatePageLoader(false);
      sfxService().playErrorSFX();
      notificationService.show({
        type: "danger",
        title: `Failed to join room.`,
        description: "Something went wrong when trying to join the room. Please try again later..."
      });
      return "Failed";
    }

    return "Success";
  }

  function disconnect() {
    appSettingsService().persistSettings();
    appService().coreService()?.websocket_disconnect();
  }

  const onEmitMessage = (msg: ServerMsg) => {
    let encoded = handleServerMessageEncode(msg);
    if (encoded != null) appService().coreService()?.websocket_send_binary(encoded);
  };

  /**
   * Posts an emit message to the server.
   *
   * @param payload - The server message payload.
   */
  function postEmitMessage(payload: ServerMsg) {
    if (appService().serverServiceDown()) return;

    if (COMMON.IS_WEB_APP) {
      onEmitMessage(payload);
      return;
    }

    if (COMMON.IS_DESKTOP_APP) {
      let encoded = handleServerMessageEncode(payload);
      if (encoded != null) appService().coreService()?.websocket_send_binary(encoded);
      return;
    }
  }

  let uploadTimeout = -1;
  const uploadClientSettings = (appSettings: AppSettings) => {
    if (!appSettings || !appService().isClientMember() || appService().offlineMode()) return;

    clearTimeout(uploadTimeout);
    uploadTimeout = window.setTimeout(() => {
      postEmitServerCommand(["UploadClientSettings", appSettings]);
    }, 1000);
  };

  /**
   * Sends a server command to the WebSocket server.
   * @param payload The payload of the server command.
   */
  function postEmitServerCommand(payload: ServerCommand) {
    postEmitMessage(["ServerCommand", payload]);
  }

  function emitProtoServerMessage(payload: ServerMessage) {
    postEmitMessage(["RawProto", payload]);
  }

  function emitProtoServerMessageOfCommand(command: ServerCommandDU) {
    postEmitMessage(["RawProto",
      ServerMessage.create({ messageType: ServerMessageType.ServerCommand, serverCommand: command })]);
  }

  function emitProtoRoomOwnerCommand(payload: RoomOwnerCommandDU) {
    let message = ServerMessage.create({ messageType: ServerMessageType.RoomOwnerCommand, roomOwnerCommand: payload });
    emitProtoServerMessage(message);
  }

  function postEmitServerModCommand(targetUser: string, payload: ServerModCommand) {
    postEmitMessage(["ServerModCommand", targetUser, payload]);
  }

  function emitUserUpdateCommand(command: UserUpdate) {
    if (!command) return;
    postEmitServerCommand(["UserUpdateCommand", command]);
  }

  function createOrUpdateRoom(params: CreateRoomParam, type: "CreateRoom" | "UpdateRoom" = "CreateRoom") {
    return new Promise((resolve) => {
      postEmitMessage([`${type}Command`, params]);
      resolve(null);
    });
  }

  function emitIsTyping(value: boolean) {
    if (value == null) return;
    isTypingSubject.next(value);
  }

  function emitChatMessage(message: string, messageReplyID?: string, messageEditID?: string, options = ChatMessageInputDataOptionsDefault) {
    if (!message || message.trim().length == 0) return;

    let input: ChatMessageInputData = {
      text: message
        .replaceAll(/\n/g, "\\\n")
        .substring(0, USER_INPUT.MaxChatMessageLength)
        .trim(),
      options: {
        ...options,
        syncToDiscord: appSettingsService().getSetting<boolean>("DISCORD_SYNC")
      }
    };

    if (isString(messageReplyID)) {
      input.options.modificationType = ["Reply", messageReplyID];
    }

    if (isString(messageEditID)) {
      input.options.modificationType = ["Edit", messageEditID];
    }

    postEmitMessage(["RoomChatMessage", input]);
  }

  function emitRoomChatServerCommand(command?: string) {
    if (!command) return;
    postEmitMessage(["RoomChatServerCommand", { command }]);
  }

  function triggerClientSelfMute(mute: boolean) {
    let noteMuteText = (mute: boolean) => mute ? "Mute" : "Unmute";
    emitUserUpdateCommand(["MutedSelf", mute]);
    if (!mute) appService().setClientHasEveryoneElseMuted(false);

    if (appService().clientIsSelfNotesMuted() == mute) {
      notificationService.show({
        title: "Mute Self",
        type: "info", duration: 2000,
        description: `No action taken since you're not ${noteMuteText(!mute).toLowerCase()}d.`
      });
      return;
    }
    appService().setClientIsSelfNotesMuted(mute);

    notificationService.show({
      title: "Mute Self",
      type: "success", duration: 2000,
      description: `You've ${noteMuteText(mute).toLowerCase()}d your notes!`
    });
  }

  if (COMMON.IS_DEV_MODE) {
    if (COMMON.IS_AUTOMATED_TEST_MODE) {
      (window as any)["websocketEvents"] = websocketEvents;
    }
  }

  return {
    initialize,
    onDisconnect,
    triggerClientSelfMute,
    emitRoomChatServerCommand,
    emitUserUpdateCommand,
    joinRoomByName,
    postEmitMessage,
    joinNextAvailableLobby,
    emitProtoServerMessageOfCommand,
    joinRoomByID,
    connect,
    emitChatMessage,
    connected,
    disconnect,
    createOrUpdateRoom,
    emitIsTyping,
    emitServerCommand: postEmitServerCommand,
    emitServerModCommand: postEmitServerModCommand,
    websocketEvents,
    logout: disconnect,
  };
}