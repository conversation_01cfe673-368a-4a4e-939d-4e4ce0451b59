import * as webWorkerProxy from 'web-worker-proxy';
import * as core_wasm from '@core/pkg/pianorhythm_core.js';

// @ts-ignore - Used for midi player loop
self["performanceNow"] = () => performance.now();

const onLoadAppEffects = (event: Event) => {
  let bytes = ((event as any).detail as Uint8Array);
  self.postMessage({ event: "app_effects", data: bytes });
};

self.onmessage = async (evt: MessageEvent) => {
  let eventData = evt.data as any;

  if (eventData == null || eventData.type && eventData.id) {
    return;
  }

  if (eventData.event == "load-wasm-module") {
    try {
      self.addEventListener("app_events", (event) => {
        let data = ((event as any).detail as Array<number>);
        self.postMessage({ event: "app_events", data: data?.[0] ?? [0] });
      });

      self.addEventListener("midi_sequencer_effects", (event) => {
        let bytes = ((event as any).detail as Uint8Array);
        self.postMessage({ event: "wasm_midi_sequencer_effects", payload: bytes }, [bytes.buffer]);
      });

      self.addEventListener("app_effects", onLoadAppEffects);

      await core_wasm.default();

      self.postMessage({
        event: "wasm-module-loaded",
        payload: {
          module: await core_wasm.get_wasm_module(),
          memory: await core_wasm.get_wasm_memory(),
        }
      });

      core_wasm.init_note_buffer_engine();
      // Adaptive flushing is now automatically started in Rust
      webWorkerProxy.proxy(core_wasm);
    } catch (err) {
      self.postMessage({ event: "error", err });
      console.error("Error loading WASM Module", err);
    }

    return;
  }
};