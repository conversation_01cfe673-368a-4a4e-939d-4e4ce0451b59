"use server";

import { getSession, UserSessionHelper } from "~/lib/server";
import { SheetMusicRequest, SheetMusicUploadResponse } from "~/models/sheet-music-dbo.models";
import { fetch } from "~/server/general.api";

export const sheetMusicUpsert = async (request: SheetMusicRequest, path: string, method: string = "POST") => {
  "use server";

  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let bodyFormData = new FormData();

    if (request.data) {
      bodyFormData.append("file", new Blob([request.data], { type: "text/plain" }));
    }
    bodyFormData.append("request", JSON.stringify(request));

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/server/sheet_music/${path}`, {
      method: method.toUpperCase(),
      headers: {
        ...UserSessionHelper.getHeaders(session.data)
      },
      body: bodyFormData
    });

    return await response.json() as SheetMusicUploadResponse;
  } catch (e) {
    console.error("[sheetMusicUpsertBase] Error:", e);
    return { error: e instanceof Error ? e.message : String(e) };
  }
};

export const sheetMusicDisapprove = async (id: string, request: SheetMusicDisapproveRequest) => {
  "use server";

  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/server/sheet_music/disapprove/${id}`, {
      method: "PATCH",
      headers: {
        'Content-Type': 'application/json',
        ...UserSessionHelper.getHeaders(session.data)
      },
      body: JSON.stringify(request)
    });

    return await response.json() as SheetMusicUploadResponse;
  } catch (e) {
    console.error("[sheetMusicDisapprove] Error:", e);
    return { error: e instanceof Error ? e.message : String(e) };
  }
};

export const sheetMusicDelete = async (id: string) => {
  "use server";

  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/server/sheet_music/${id}`, {
      method: "DELETE",
      headers: {
        'Content-Type': 'application/json',
        ...UserSessionHelper.getHeaders(session.data)
      }
    });

    return await response.json() as SheetMusicUploadResponse;
  } catch (e) {
    console.error("[sheetMusicDelete] Error:", e);
    return { error: e instanceof Error ? e.message : String(e) };
  }
};

export const sheetMusicApprove = async (id: string) => {
  "use server";

  try {
    let session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/server/sheet_music/approve/${id}`, {
      method: "PATCH",
      headers: {
        'Content-Type': 'application/json',
        ...UserSessionHelper.getHeaders(session.data)
      }
    });

    return await response.json() as SheetMusicUploadResponse;
  } catch (e) {
    console.error("[sheetMusicApprove] Error:", e);
    return { error: e instanceof Error ? e.message : String(e) };
  }
};
