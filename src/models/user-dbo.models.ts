import { z } from "zod";

// Basic Union type used throughout the user model
export const BasicUnionZ = z.object({
  Case: z.string(),
  Fields: z.array(z.string()).default([])
});

export type BasicUnion = z.infer<typeof BasicUnionZ>;

// Avatar World Data DTO
export const AvatarWorldDataDtoZ = z.object({
  // TODO: Define avatar world data structure when needed
});

export type AvatarWorldDataDto = z.infer<typeof AvatarWorldDataDtoZ>;

// User World Data
export const UserWorldDataZ = z.object({
  // Skip character_data_json for now until avatars are back
  orchestraModelCustomizationDataJSON: z.string().nullish(),
  avatarWorldData: AvatarWorldDataDtoZ
});

export type UserWorldData = z.infer<typeof UserWorldDataZ>;

// User Permissions
export const UserPermissionsZ = z.object({
  uploadProfileImages: z.boolean().default(false),
  setNickname: z.boolean().default(false),
  setStatusText: z.boolean().default(false),
  setColor: z.boolean().default(false),
  uploadSheetMusic: z.boolean().default(false),
  uploadMidiMusic: z.boolean().default(false),
  selfHostRooms: z.boolean().default(false),
  setProfileDescription: z.boolean().default(false)
});

export type UserPermissions = z.infer<typeof UserPermissionsZ>;

// User Notification Settings
export const UserNotificationSettingsZ = z.object({
  // TODO: Define notification settings structure when needed
});

export type UserNotificationSettings = z.infer<typeof UserNotificationSettingsZ>;

// User Server Settings
export const UserServerSettingsZ = z.object({
  mutedSelf: z.boolean().default(false),
  notificationSettings: UserNotificationSettingsZ
});

export type UserServerSettings = z.infer<typeof UserServerSettingsZ>;

// User Settings
export const UserSettingsZ = z.object({
  color: z.string().nullish(),
  nickname: z.string().nullish(),
  statusText: z.string().nullish()
});

export type UserSettings = z.infer<typeof UserSettingsZ>;

// User Friend Info
export const UserFriendInfoZ = z.object({
  friendTag: z.string(),
  friendID: z.string(),
  created: z.date()
});

export type UserFriendInfo = z.infer<typeof UserFriendInfoZ>;

// Pending Friend Request
export const PendingFriendRequestZ = z.object({
  usertag: z.string(),
  uuid: z.string(),
  createdDate: z.string()
});

export type PendingFriendRequest = z.infer<typeof PendingFriendRequestZ>;

// Block User
export const BlockUserZ = z.object({
  usertag: z.string(),
  ip: z.string().nullish(),
  created: z.date()
});

export type BlockUser = z.infer<typeof BlockUserZ>;

// User Info
export const UserInfoZ = z.object({
  tag: z.string(),
  email: z.string().nullish(),
  profileDescription: z.string().nullish(),
  friends: z.array(UserFriendInfoZ).default([]),
  pendingFriendRequests: z.array(PendingFriendRequestZ).default([]),
  blocked: z.array(BlockUserZ).default([]),
  roles: z.array(BasicUnionZ).default([]),
  badges: z.array(BasicUnionZ).default([]),
  connectionType: BasicUnionZ,
  isAPIOnly: z.boolean().default(false)
});

export type UserInfo = z.infer<typeof UserInfoZ>;

// Billing Meta
export const BillingMetaZ = z.object({
  subscriptionID: z.string(),
  priceID: z.string(),
  productID: z.string(),
  nextBillingDate: z.date().nullish(),
  currencyCode: z.string().nullish(),
  pricePerUnit: z.string().nullish()
});

export type BillingMeta = z.infer<typeof BillingMetaZ>;

// User Billing Settings
export const UserBillingSettingsZ = z.object({
  currentPlan: BasicUnionZ,
  customerID: z.string().nullish(),
  cancelationInProcess: z.boolean().default(false),
  meta: BillingMetaZ.nullish()
});

export type UserBillingSettings = z.infer<typeof UserBillingSettingsZ>;

// User Meta
export const UserMetaZ = z.object({
  // TODO: Define user meta structure when needed
});

export type UserMeta = z.infer<typeof UserMetaZ>;

// Main UserDbo schema
export const UserDboZ = z.object({
  _id: z.any(), // MongoDB ObjectId
  uuid: z.string(),
  username: z.string(),
  // password: z.string().nullish(),
  info: UserInfoZ,
  settings: UserSettingsZ,
  serverSettings: UserServerSettingsZ,
  permissions: UserPermissionsZ,
  billingSettings: UserBillingSettingsZ,
  modified: z.date().nullish(),
  meta: UserMetaZ,
  syncedClientSettings: z.string().nullish(),
  emailVerified: z.boolean().default(false)
  // worldData: UserWorldDataZ.nullish(),
});

export type UserDbo = z.infer<typeof UserDboZ>;

// User DTO for API responses
export const UserDtoZ = z.object({
  id: z.string(),
  username: z.string(),
  usertag: z.string(),
  nickname: z.string().nullish(),
  color: z.string().nullish(),
  statusText: z.string().nullish(),
  profileDescription: z.string().nullish(),
  roles: z.array(z.string()).default([]),
  badges: z.array(z.string()).default([]),
  emailVerified: z.boolean().default(false),
  isAPIOnly: z.boolean().default(false),
  lastModified: z.string().nullish()
});

export type UserDto = z.infer<typeof UserDtoZ>;

// Map UserDbo to UserDto
export const mapUserDboToDto = (user: UserDbo): UserDto => {
  return {
    id: user.uuid,
    username: user.username,
    usertag: user.info.tag,
    nickname: user.settings.nickname,
    color: user.settings.color,
    statusText: user.settings.statusText,
    profileDescription: user.info.profileDescription,
    roles: user.info.roles.map(role => role.Case),
    badges: user.info.badges.map(badge => badge.Case),
    emailVerified: user.emailVerified,
    isAPIOnly: user.info.isAPIOnly,
    lastModified: user.modified?.toISOString()
  };
};

// User constants
export namespace UserConst {
  export const MinUsernameLength = 3;
  export const MaxUsernameLength = 32;
  export const MaxEmailLength = 254;
  export const MaxNicknameLength = 32;
  export const MaxStatusTextLength = 128;
  export const MaxProfileDescriptionLength = 1024;
  export const MaxCustomBadgeLength = 24;
}
