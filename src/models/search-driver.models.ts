import { z } from "zod";

const SortDirectionZ = z.union([z.literal("asc"), z.literal("desc"), z.literal("")]);
const FilterTypeZ = z.union([z.literal("any"), z.literal("all"), z.literal("none")]);
const FieldValueZ = z.union([z.string(), z.number(), z.boolean(), z.array(z.union([z.string(), z.number(), z.boolean()]))]);
const FilterValueValueZ = FieldValueZ;
const FilterValueRangeZ = z.object({
  from: FieldValueZ.optional(),
  name: z.string(),
  to: FieldValueZ.optional()
});
const FilterValueZ = z.union([FilterValueValueZ, FilterValueRangeZ]);
const FacetValueZ = z.object({
  count: z.number(),
  value: FilterValueZ,
  selected: z.boolean().optional()
});

const FacetTypeZ = z.union([z.literal("range"), z.literal("value")]);

export const FilterZ = z.object({
  field: z.string(),
  type: FilterTypeZ,
  values: z.array(z.string())
});

const RangeZ = z.object({
  from: z.number(),
  to: z.number().optional(),
  name: z.string().optional()
});

const FacetZ = z.object({
  type: z.string(),
  ranges: z.array(RangeZ).optional()
});

export const FacetsZ = z.object({
  key: z.string(),
  facet: FacetZ
});

const RequestZ = z.object({
  current: z.number().default(1),
  filters: z.array(FilterZ).default([]),
  resultsPerPage: z.number().default(10),
  searchTerm: z.string().optional(),
  sortDirection: z.string().optional(),
  sortField: z.string().optional()
});

const QueryZ = z.object({
  facets: z.array(FacetsZ).default([]),
  search_fields: z.array(z.string()).default([])
});

export const SearchDriverInputZ = z.object({
  request: RequestZ,
  query: QueryZ,
  usertag: z.string().optional()
});

const ServerFacetTypeZ = z.union([
  z.literal("Value"),
  z.literal("Range"),
  z.literal("Bool"),
  // z.tuple([z.literal("Bool"), z.boolean()]),
  z.literal("Unknown")
]);

const ServerFacetZ = z.object({
  type: ServerFacetTypeZ,
  ranges: z.array(z.unknown()).optional(),
  size: z.number().optional()
});

const ServerFacetsZ = z.object({
  key: z.string(),
  facet: ServerFacetZ,
  data: z.array(z.tuple([z.string(), z.array(z.object({ count: z.number(), value: z.string() }))])).optional()
});

const ServerFacetResponseZ = z.object({
  key: z.string(),
  type: z.union([z.literal("Value"), z.literal("Range")]),
  data: z.array(z.tuple([z.string(), z.array(z.object({ count: z.number(), value: z.string() }))])).optional()
});

const MetaZ = z.object({
  page: z.object({
    current: z.number(),
    size: z.number(),
    total_pages: z.number(),
    total_results: z.number()
  }),
  request_id: z.string(),
  alerts: z.array(z.unknown()),
  warning: z.array(z.unknown())
});

const ResultEntryZ = z.object({
  raw: z.union([z.string(), z.number(), z.boolean()]).optional(),
  snippet: z.union([z.string(), z.number(), z.boolean()]).optional()
});

const SearchResultZ = z.record(z.union([ResultEntryZ, z.any()]));

