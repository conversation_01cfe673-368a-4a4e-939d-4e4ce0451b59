import mapKeys from "lodash-es/mapKeys";
import { COMMON } from "./const.common";
import { UserRegisterForm } from "~/lib/schema";
import isString from "lodash-es/isString";

const localRootURL = `http://127.0.0.1:${COMMON.PORT}`;

export const getNow = () => {
  if (self.performance && typeof self.performance.now === 'function') {
    return self.performance.now();
  } else {
    return Date.now();
  }
};

export function isDefined<T>(value: T | null | undefined): value is NonNullable<T> {
  return value !== null && value !== undefined;
}

export function roundNumberToTwoDecimals(value: number) {
  return Math.round((value + Number.EPSILON) * 100) / 100;
}

export function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export const splitAndJoinWordsByCapitalLetter = (input: string) => {
  if (!input) return;
  return input.split(/(?=[A-Z])/).join(" ");
};

export function arrayEquals(a: number[], b: number[]) {
  return Array.isArray(a) &&
    Array.isArray(b) &&
    a.length === b.length &&
    a.every((val, index) => val === b[index]);
}

export const validURLRegex = "(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})";

export function substringEllipses(message: string | undefined | null, length = 30) {
  if (message == null) return message;
  let regex = new RegExp(`(.{${length}})..+`);
  return message.replace(regex, "$1…");
}

export function arrayBufferToBase64(buffer: ArrayBuffer) {
  let binary = '';
  let bytes = new Uint8Array(buffer);
  let len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes?.[i] ?? 0);
  }
  return window.btoa(binary);
}

export function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result as string);
    reader.onerror = () => reject("failed to convert blob to base64");
    reader.readAsDataURL(blob);
  });
}

export function obscureEmail(email?: string) {
  if (!email) return email;
  const [name, domain] = email.split('@');
  return `${name?.[0]}${new Array(name?.length ?? 0).join('*')}@${domain}`;
}

export const getLocalUrl = (rootURL?: string) => `${rootURL || localRootURL}/models/`;

export const canCreateSharedArrayBuffer = () => COMMON.IS_DESKTOP_APP || self.crossOriginIsolated || window.crossOriginIsolated || "SharedArrayBuffer" in window;

export function oneTimeEventListener(element: Element, eventName: string, handler: any) {
  element.addEventListener(eventName, function (e) {
    return handler(e);
  }, { once: true });
}

export function map(current: number, in_min: number, in_max: number, out_min: number, out_max: number): number {
  return ((current - in_min) * (out_max - out_min)) / (in_max - in_min) + out_min;
}

export const encodeForProto = (data: any): any => {
  return mapKeys(data, (_, key) => key.replaceAll("_", ""));
};

export const arrayChunks = (array: any[], chunk_size: number) => {
  try {
    return Array(Math.ceil(array.length / chunk_size)).fill(0).map((_, index) => index * chunk_size).map(begin => array.slice(begin, begin + chunk_size));
  } catch (e) {
    return [];
  }
};


export function userGestures() {
  return [
    new Promise((resolve) => oneTimeEventListener(window as any, "mousedown", resolve)),
    new Promise((resolve) => oneTimeEventListener(window as any, "keydown", resolve)),
    new Promise((resolve) => oneTimeEventListener(window as any, "click", resolve)),
    new Promise((resolve) => oneTimeEventListener(window as any, "touchend", resolve)),
    new Promise((resolve) => oneTimeEventListener(window as any, "contextmenu", resolve))
  ];
}

export function wrapObj(targetObject: any, onFunctionWrap: (targetObject: any, fn: string) => void) {
  for (var fn in targetObject) {
    var type = typeof (targetObject[fn]);
    if (type === 'function') {
      onFunctionWrap(targetObject, fn);
    }
  }
}

export const getFileNameFromPath = (file: string) => {
  if (!file) return file;

  let fileName = file;
  let slashIndex = file.lastIndexOf("\\");
  if (slashIndex == -1) slashIndex = file.lastIndexOf("/");
  if (slashIndex != -1) {
    fileName = file.substring(slashIndex + 1);
  }

  return fileName;
};

export function createLoginFormData(username: string, password?: string, isGuest?: boolean) {
  let formData = new FormData();
  formData.append("username", username);
  formData.append("isGuest", String(isGuest));
  formData.append("password", password ?? "");
  return formData;
}

export function createSimpleEmailFormData(email: string) {
  let formData = new FormData();
  formData.append("email", email);
  return formData;
}

export function createRegisterFormData(form: UserRegisterForm) {
  let formData = new FormData();
  formData.append("username", form.username);
  formData.append("email", form.email);
  formData.append("password", form.password);
  formData.append("password2", form.password2);
  formData.append("tos", String(form.tos));
  return formData;
}

export const getRandomItem = (items: any[]) => items[Math.floor(Math.random() * items.length)];

export function isSoundfontFilePath_windows(path: string) {
  return /^[a-zA-Z]:\\(((?![<>:"/\\|?*]).)+((?<![ .])\\)?)*$/gi.test(path);
}

export function isMobile() {
  if (!navigator || !navigator.userAgent) return false;

  // device detection
  return (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
    || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substring(0, 4)));
}

export const capitalizeFirstLetter = ([first, ...rest]: any, locale = navigator.language || "en-US") =>
  first === undefined ? '' : first.toLocaleUpperCase(locale) + rest.join('');

export function stringToColor(str: string | null) {
  if (str == null) return "#000000";

  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  let colour = '#';
  for (let i = 0; i < 3; i++) {
    let value = (hash >> (i * 8)) & 0xFF;
    colour += ('00' + value.toString(16)).substr(-2);
  }

  return colour;
}

export function generateUUID() { // Public Domain/MIT
  let d = new Date().getTime();//Timestamp
  let d2 = ((typeof performance !== 'undefined') && performance.now && (performance.now() * 1000)) || 0;//Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16;//random number between 0 and 16
    if (d > 0) {//Use timestamp until depleted
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {//Use microseconds since page-load if supported
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
}

export function is_colliding($div1?: Element | null, $div2?: Element | null) {
  if (!$div1 || !$div2) return false;

  let rect1 = $div1.getBoundingClientRect();
  let rect2 = $div2.getBoundingClientRect();

  return !(rect1.right < rect2.left ||
    rect1.left > rect2.right ||
    rect1.bottom < rect2.top ||
    rect1.top > rect2.bottom);
};

export function rainbowColorArray(size: number): string[] {
  if (size == null) size = 128;
  let rainbow = new Array(size);

  function sin_to_hex(i: number, phase: number) {
    let sin = Math.sin(Math.PI / size * 2 * i + phase);
    let int = Math.floor(sin * 127) + 128;
    let hex = int.toString(16);
    return hex.length === 1 ? "0" + hex : hex;
  }

  for (let i = 0; i < size; i++) {
    let red = sin_to_hex(i, 0 * Math.PI * 2 / 3); // 0   deg
    let blue = sin_to_hex(i, 1 * Math.PI * 2 / 3); // 120 deg
    let green = sin_to_hex(i, 2 * Math.PI * 2 / 3); // 240 deg
    rainbow[i] = "#" + red + green + blue;
  }

  return rainbow;
}

export const removeBase64Header = (input: string) => {
  return input?.split(',')?.pop();
};

/**
 * Converts an ArrayLike or ArrayBufferLike object to a string using UTF-16 encoding.
 * @param data - The array-like or buffer-like data to convert.
 * @returns The resulting string.
 */
export const ab2str = (data: ArrayLike<number> | ArrayBufferLike) => {
  return String.fromCharCode.apply(null, new Uint16Array(data) as any);
};

export namespace FormHelper {
  export const isNotEmpty = ({ value }: { value: string; }) =>
    (isString(value) && Boolean(value)) ? false : "Must be a valid string and non-empty";
}
