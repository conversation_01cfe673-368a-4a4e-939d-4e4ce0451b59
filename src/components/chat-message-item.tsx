import { Box, HStack, Skeleton, VStack } from '@hope-ui/solid';
import clsx from 'clsx';
import { cloneDeep, isEmpty, uniq } from 'lodash-es';
import { Subscription } from 'rxjs';
import {
  Component,
  createEffect,
  createSignal,
  lazy,
  Match,
  onCleanup,
  onMount,
  Show,
  Suspense,
  Switch
} from 'solid-js';
import { useService } from 'solid-services';
import { useChatRecordContext } from '~/contexts/chat-record.context';
import { ApiUserRecordProvider } from '~/contexts/user.context';
import { Item, Menu, Separator, useContextMenu } from '~/packages/solid-contextmenu';
import { Roles, rolesToJSON } from '~/proto/user-renditions';
import css from '~/sass/chat.module.sass';
import { scrapeUrl } from '~/server/general.api';
import AppService from '~/services/app.service';
import ChatService from '~/services/chat.service';
import EmojifyService from '~/services/emojify.service';
import I18nService from '~/services/i18n.service';
import AppSettingsService from '~/services/settings-storage.service';
import SoundEffectsService from '~/services/sound-effects.service';
import UsersService from '~/services/users-service';
import WebsocketService from '~/services/websocket.service';
import { WebpageMetaDataOutput } from '~/types/api.types';
import { ChatMessageRecord } from '~/types/chat.types';
import { ApiUserDto } from '~/types/user.types';
import { arrayBufferToBase64, validURLRegex } from '~/util/helpers';
import { tryDecodeURI } from '~/util/helpers.dom';
import SwalPR from '~/util/sweetalert';
import { deleteMessageByID, triggerDeleteMessagesWarning } from './chat.common';
import { SolidMarkDownText } from './solid-markdown-text';

// @ts-ignore
import { VirtualItemProps } from "@minht11/solid-virtual-container";

const UserMiniProfileCardTooltip = lazy(() => import('~/components/user-mini-profile-card-tooltip'));
const UserProfileImage = lazy(() => import('~/components/user-profile-image'));

const ChatMessageItemOptions = lazy(() => import("./chat-message-item-options"));
const ChatMessageReplyItem = lazy(() => import("./chat-message-item-reply"));

/**
 * Renders the date and time of a chat message.
 * @component
 * @param {Object} props - The component props.
 * @param {Date} props.time - The date and time to be displayed.
 * @returns {JSX.Element} The rendered date and time.
 */
const ChatMessageDate: Component<{ time?: Date; }> = (props) => {
  let targetString = props.time?.toLocaleString([], {dateStyle: 'short', timeStyle: 'short'});

  const onRef = (element: HTMLDivElement) => {
    if (!element) return;
    if (element.offsetWidth < 100) {
      element.style.fontSize = "10px";
    }
  };

  return <Box
    fontSize={10}
    ref={onRef}
    color="$neutral11"
  >{targetString}
  </Box>;
};

const ChatMessageEmbeddable: Component<{ url: string; onLoaded?: () => void; }> = (props) => {
  const [loading, setLoading] = createSignal<boolean>(false);
  const [output, setOutput] = createSignal<WebpageMetaDataOutput>();
  const i18nService = useService(I18nService);
  const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);

  onMount(async () => {
    try {
      setLoading(true);
      let data = await scrapeUrl(props.url);
      if (!data) return;

      setOutput(data);

      if (data.imageUrl) {
        let response = await fetch(data.imageUrl);
        if (!response.ok) return;
        let responseData = await response.arrayBuffer();

        let contentType = response.headers.get("content-type") || "image/png";
        let output = `data:${contentType};base64,${arrayBufferToBase64(responseData)}`;

        setOutput(v => ({
          ...v,
          url: props.url,
          imageURL: output
        }));
      }
    } catch {
    }

    setLoading(false);
  });

  return <>
    <Switch fallback={<SolidMarkDownText text={props.url}/>}>

      <Match when={loading()}>
        <Skeleton w="100%" h={115}/>
        {output()?.url && <Box
            as="a"
            cursor={"pointer"}
            color="gray"
            href={output()?.url}
        >{output()?.url}</Box>}
      </Match>

      <Match when={output()?.imageUrl}>
        <VStack
          w="100%"
          h="100%"
          background={"rgba(0,0,0,0.2)"}
          borderRadius={5}
          cursor={"pointer"}
          transition="background 0.2s ease, filter 0.2s ease"
          style={{"filter": `grayscale(40%)`}}
          _hover={{"filter": `grayscale(0%) !important`}}
          onmousedown={(evt: MouseEvent) => {
            let targetURL = output()?.url;
            if (evt.button == 0 && targetURL) {
              let openURL = () => window.open(targetURL);
              if (!appSettingsService().getSetting("ONLINE_WARN_ABOUT_EXTERNAL_LINKS")) {
                openURL();
              } else {
                SwalPR(sfxService).fire({
                  html: i18nService().t_roomPage("generalMessages.openingExternalLinks"),
                  showCancelButton: true,
                  icon: "warning",
                  cancelButtonText: "Nah, sorry...",
                  confirmButtonText: "Yes!",
                  showConfirmButton: true,
                  allowEscapeKey: true,
                }).then((res) => {
                  if (res.isConfirmed) openURL();
                });
              }
            }
          }}
        >
          <Box
            w="100%"
            h={115}
            borderRadius={5}
            style={{
              "background-size": "cover",
              "background-position": "center center",
              "background-repeat": "no-repeat",
              "background-image": `
              url(${output()?.imageUrl}),
              url('https://via.placeholder.com/150?text=${encodeURI(output()?.site || "")}')
            `
            }}
            ref={() => {
              props.onLoaded?.();
            }}
          >
          </Box>
          <Box w="100%" h="100%">
            <VStack
              display={"flex !important"}
              alignItems="flex-start"
              spacing={"$0_5"}
              paddingLeft={10}
              paddingBottom={15}
            >
              {output()?.url && <Box color="lightgray" textTransform={"uppercase"} fontSize={10}>
                {`${output()?.url.substring(0, 40)}...`}
              </Box>
              }
              <VStack
                __tooltip_title={
                  <VStack spacing={"$2"} alignItems="flex-start" maxW={300}>
                    <Box>Site: <Box as="span" color="$accent1">{output()?.site}</Box></Box>
                    <Box>{tryDecodeURI(output()?.description || "No Description")}</Box>
                  </VStack>
                }
                alignItems="flex-start" display={"flex !important"} spacing={"$2"}
              >
                {output()?.site && <Box color="$accent1" fontWeight={"bold"} fontSize={14}> {output()?.site} </Box>}
                {output()?.title && <Box> {output()?.title} </Box>}
              </VStack>
            </VStack>
          </Box>
        </VStack>
      </Match>
    </Switch>
  </>;
};

const RolesThatCanBeDisplayedAsTags = [
  "isBot",
  "isDev",
  "isMod",
  "isPlugin",
  "isProMember",
  "isRoomOwner"
].map(x => x.toLowerCase());

const Timestamp: Component = () => {
  const [chatRecord] = useChatRecordContext();

  return (
    <Box className={css.timeStampOpacity}>
      <Box as="span"
           className={css.chatTimestamp}>{
        chatRecord().timestamp?.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'})
      }
      </Box>
    </Box>
  );
};

export const ChatMessageUsername: Component<{ fetchedUserAccount: ApiUserDto; }> = (props) => {
  const [chatRecord] = useChatRecordContext();
  const [hasRoleThatCanBeDisplayed, setHasRoleThatCanBeDisplayed] = createSignal(false);
  const [openMiniProfile, setOpenMiniProfile] = createSignal(false);
  const [color] = createSignal<string>(chatRecord().usernameColor || "$neutral12");
  const [user, setUser] = createSignal<ApiUserDto>();

  const emojifyService = useService(EmojifyService);
  const [openMiniProfileTimeout, setOpenMiniProfileTimeout] = createSignal<number>(-1);
  const [closeMiniProfileTimeout, setCloseMiniProfileTimeout] = createSignal<number>(-1);

  onMount(async () => {
    let roles = chatRecord().getRoles();
    if (roles.length == 0) {
      roles.push(Roles.GUEST);
    }

    setUser({
      ...props.fetchedUserAccount,
      usertag: chatRecord().usertag,
      username: chatRecord().displayName,
      color: color(),
      roles: roles.map(rolesToJSON)
    } as ApiUserDto);

    setHasRoleThatCanBeDisplayed(
      Object
        .entries(chatRecord().roles)
        .some(([x, value]) => RolesThatCanBeDisplayedAsTags.includes(x.toLowerCase()) && value));

    if (chatRecord().roles.isGuest || chatRecord().roles.isSystem) return;
  });

  const closeTooltip = (immediate = false) => {
    clearTimeout(openMiniProfileTimeout());
    clearTimeout(closeMiniProfileTimeout());
    if (immediate) return setOpenMiniProfile(false);

    setCloseMiniProfileTimeout(window.setTimeout(() => {
      setOpenMiniProfile(false);
    }, 100));
  };

  onCleanup(() => {
    closeTooltip(true);
  });

  const UserTagsContainer = () => {
    const onRef = (element: HTMLDivElement) => {
      if (!element) return;
    };

    return (
      <HStack
        ref={onRef}
        className={css.usertagsContainer}
        spacing={"$1"}
        overflow={"hidden"}
      >
        {(chatRecord().roles.isPlugin) && <Box className="pr-tag-chatmsg" background={"#7289da"}>PLUGIN</Box>}
        {(chatRecord().roles.isBot) && <Box className="pr-tag-chatmsg" background={"#7289da"}>BOT</Box>}
        {(chatRecord().roles.isDev) && <Box className="pr-tag-chatmsg" background={"black"}>DEV</Box>}
        {(chatRecord().roles.isMod) && <Box className="pr-tag-chatmsg" background={"$accent1"}>MOD</Box>}
        {(chatRecord().roles.isRoomOwner) && <Box className="pr-tag-chatmsg" background={"green"}>ROOM OWNER</Box>}
        {(chatRecord().roles.isProMember) &&
            <Box className={clsx(["pr-tag-chatmsg", "rainbow-border-shine-no-anim"])} marginLeft={2} marginRight={2}
                 zIndex={2}>PRO</Box>}
      </HStack>
    );
  };

  return (<>
    <HStack spacing={"$1"} marginTop={3}>
      <HStack spacing={"$1"}>
        <Suspense>
          {user() &&
              <ApiUserRecordProvider value={{default: user()}}>
                  <Suspense>
                      <UserMiniProfileCardTooltip
                          placement="top"
                          forChat
                          opened={openMiniProfile()}
                          closeTooltip={() => closeTooltip()}
                      >
                          <Box
                              onmouseenter={() => {
                                clearTimeout(closeMiniProfileTimeout());
                                clearTimeout(openMiniProfileTimeout());
                                setOpenMiniProfileTimeout(window.setTimeout(() => {
                                  setOpenMiniProfile(true);
                                }, 375));
                              }}
                              onmouseleave={() => closeTooltip()}
                              color={color()}
                              className={css.userName}>{emojifyService?.().decode(chatRecord().displayName)}</Box>
                      </UserMiniProfileCardTooltip>
                  </Suspense>
              </ApiUserRecordProvider>
          }
        </Suspense>

        {!hasRoleThatCanBeDisplayed() &&
            <Box className={css.showOptionsContainer}>
                <ChatMessageDate time={chatRecord().timestamp}/>
            </Box>
        }
      </HStack>

      <UserTagsContainer/>

      {/* Show the date and time if the user has no roles that can be displayed */}
      {hasRoleThatCanBeDisplayed() &&
          <Box className={css.showOptionsContainer}>
              <ChatMessageDate time={chatRecord().timestamp}/>
          </Box>
      }
    </HStack>
  </>);
};

const validURLReg = new RegExp(validURLRegex);
const TIME_DIFF_FOR_DISPLAY_USERNAME_FOR_SAME_USER = 1; //in minutes

type ChatMessageItemProps = {
  onLoaded?: () => void;
  onMoreOptionsClick?: () => void;
};

const ChatMessageItem: Component<VirtualItemProps<ChatMessageRecord> & ChatMessageItemProps> = (props) => {
  const [chatRecord] = useChatRecordContext();
  const [displayUsername, setDisplayUsername] = createSignal<boolean>(true);
  const [message, setMessage] = createSignal(chatRecord().sanitizedMessage);
  const [isURLForOpenMeta, setIsUrlForOpenMeta] = createSignal(false);
  const [wasModified, setWasModified] = createSignal(chatRecord().wasModified);
  const [cssClasses, setCssClasses] = createSignal<string[]>([]);
  const [messageHasClientTagged, setMessageHasClientTagged] = createSignal(false);
  const appService = useService(AppService);
  const appSettingsService = useService(AppSettingsService);
  const chatService = useService(ChatService);
  const websocketService = useService(WebsocketService);

  let onLoadedTimeout = -1;
  const [subscriptions, setSubscriptions] = createSignal<Subscription[]>([]);
  const usersService = useService(UsersService);
  const [user, setUser] = createSignal<ApiUserDto>(ApiUserDto.Default());
  const MENU_ID = `chat-message-item-${chatRecord().id}`;
  const {onShowContextMenu} = useContextMenu({id: MENU_ID});
  let elementRef: HTMLDivElement | null = null;

  async function updateRecord(_record: ChatMessageRecord) {
    if (_record && _record?.sanitizedMessage && _record.id == chatRecord().id) {
      setMessage(_record.sanitizedMessage);
      setWasModified(_record.wasModified);
    }
  }

  onMount(async () => {
    let _record = cloneDeep(chatRecord());
    let currentRecordIndex = chatService().getIndexByMessageID(_record.id);
    let _prevRecord = chatService().getMessageByIndex(currentRecordIndex - 1);
    if (_record && !_record.isSystem) calculateDisplayUsername(_record, _prevRecord);

    let subscription = chatService().deletedMessagesEvents.subscribe(v => {
      if (v.nextMessageID == _record.id) {
        let _prevRecord = chatService().getMessageByIndex(v.index - 1);
        let record = chatService().getMessageByID(_record.id);

        // Recalculate display username based on previous message after a message, before
        // this current one, gets deleted
        if (record && _prevRecord) calculateDisplayUsername(record, _prevRecord);
      }
    });
    setSubscriptions([...subscriptions(), subscription]);

    let editedMessagesSubscription = chatService().editedMessagesEvents.subscribe(async editedMessage => {
      if (editedMessage?.id == _record?.id) {
        await updateRecord(editedMessage);
      }
    });
    setSubscriptions([...subscriptions(), editedMessagesSubscription]);

    let input = {
      ...ApiUserDto.Default(),
      usertag: _record.usertag,
      username: _record.displayName,
      roles: _record.getRoles().map(rolesToJSON)
    };

    if (_record.isMember()) {
      try {
        let fetchedUserAccount = await usersService().fetchUserBasicAccount(_record.usertag);
        if (fetchedUserAccount) input = {
          ...input,
          profileImageLastModified: fetchedUserAccount.profileImageLastModified,
          profileBackgroundImageLastModified: fetchedUserAccount.profileBackgroundImageLastModified,
          lastOnline: fetchedUserAccount.lastOnline ? new Date(fetchedUserAccount.lastOnline) : undefined,
        };
      } catch {
      }
    }

    setUser(input);
  });

  createEffect(() => {
    appSettingsService().settingSaved(); //Hack to retrigger update
    setMessageHasClientTagged(
      appSettingsService().getSetting<boolean>("ALLOW_USERS_TO_NOTIFY_ME") &&
      !appService().isClientInDoNotDisturb() &&
      chatService().messsageHasTaggedUser(message(), appService().client().usertag)
    );
  });

  createEffect(() => {
    let classes = [
      (messageHasClientTagged() && css.chatMessageClientTagged),
      (chatService().activeMessageItemIndexToShowOptionsMenu() == chatRecord().id && css.chatMessageHighlighted),
      (chatService().activeChatCtxID() == chatRecord().id && css.chatMessageHighlighted),
      (chatService().chatMessageToHighlight() == chatRecord().id && css.chatMessageHighlighted),
      (chatService().chatMessageBeingRepliedTo() == chatRecord().id && css.chatMessageBeingRepliedTo),
      (chatService().chatMessageBeingEdited() == chatRecord().id && css.chatMessageBeingEdited),
      (chatService().chatMessagesSetToBeDeleted().includes(chatRecord().id) && css.chatMessageSetToBeDeleted)
    ].filter(Boolean);

    setCssClasses(uniq(classes) as any);
  });

  createEffect(async () => {
    setMessage(chatRecord().sanitizedMessage);
    setWasModified(chatRecord().wasModified);
  });

  createEffect(() => {
    try {
      appSettingsService().settingSaved();

      if (!appSettingsService().getSetting("CHAT_ENABLE_IMAGE_URL_PREVIEW")) {
        setIsUrlForOpenMeta(false);
        return;
      }

      let output = validURLReg.test(message());
      setIsUrlForOpenMeta(output);
    } catch {
    }
  });

  onCleanup(() => {
    window.clearTimeout(onLoadedTimeout);
    subscriptions().forEach(x => x.unsubscribe());
    elementRef?.removeEventListener("animationend", onAnimationEnd);
  });

  const canReportMessage = () => {
    return false;
  };

  const canDeleteChatMessage = () => {
    let isUserMod = appService().client().isMod;
    let isSystemMessage = chatRecord().isSystem || chatRecord().socketID?.toLowerCase().indexOf("system") == 0;
    let doesChatMessageBelongToClient = appService().client().usertag == chatRecord().usertag || appService().client().socketID == chatRecord().socketID;
    return isUserMod || doesChatMessageBelongToClient || appService().isClientRoomOwner() && !isSystemMessage;
  };

  const canDeleteChatMessageByUser = () => {
    let isUserMod = appService().client().isMod;
    let isClientRoomOwner = appService().isClientRoomOwner();
    let hasUsertag = Boolean(chatRecord().usertag);
    let isSystemMessage = chatRecord().isSystem || chatRecord().socketID?.toLowerCase().indexOf("system") == 0;
    return isUserMod || isClientRoomOwner && hasUsertag && !isSystemMessage;
  };

  function calculateDisplayUsername(record: ChatMessageRecord, prevRecord: ChatMessageRecord | undefined) {
    let prevAndCurrentIsSameUser = prevRecord?.usertag == record.usertag;

    if (
      prevRecord?.usertag != record.usertag ||
      record.messageReplyID ||
      (prevAndCurrentIsSameUser && prevRecord.isPlugin && !record.isPlugin)
    ) {
      setDisplayUsername(true);
      return;
    }

    if (record && prevRecord && prevRecord.usertag == record.usertag) {
      let dateDiff = Math.abs(prevRecord.timestamp.getMinutes() - record.timestamp.getMinutes());
      setDisplayUsername(dateDiff >= TIME_DIFF_FOR_DISPLAY_USERNAME_FOR_SAME_USER);
    } else {
      setDisplayUsername(true);
    }
  }

  const onAnimationEnd = (event: AnimationEvent) => {
    if (event.animationName != css.MessageIn) return;
    (event.target as HTMLDivElement)?.classList.remove(css.chatMessageAnimateIn!);
    props.onLoaded?.();
  };

  return (<>
    <div
      role="listitem"
      id={`chat-message-${chatRecord().id}-container`}
      data-testid={"chat-message-item"}
      class={css.chatMessageWrapper}
      oncontextmenu={onShowContextMenu}
    >
      <Show when={user().usertag || user().username} fallback={<Skeleton h={50} mb={10}></Skeleton>}>
        <ApiUserRecordProvider value={{socketID: chatRecord().socketID, default: user()}}>
          <Box className={css.showOptionsContainer}>
            <Suspense>
              <ChatMessageItemOptions record={chatRecord()} onMoreOptionsClick={props.onMoreOptionsClick}/>
            </Suspense>
          </Box>

          <VStack
            id={`chat-message-${chatRecord().id}`}
            maxWidth={chatService().chatMaximized() ? "inherit" : chatService().ContainerWidth - 20}
            alignItems={"flex-start"}
            class={clsx([
              css.chatMessage,
              css.chatMessageAnimateIn,
              ...cssClasses()
            ])}
            ref={(element: HTMLDivElement) => {
              element.addEventListener("animationend", onAnimationEnd);
              elementRef = element;
            }}
          >
            {chatRecord().messageReplyID &&
                <Suspense>
                    <ChatMessageReplyItem replyID={chatRecord().messageReplyID as string}/>
                </Suspense>
            }

            <HStack spacing={"$2"} width="100%">
              {/* Avatar */}
              {(!isEmpty(chatRecord().username) && displayUsername() && !chatRecord().isSystem) &&
                  <Suspense fallback={<div>Loading...</div>}>
                      <UserProfileImage
                          className={css.userAvatar}
                          forceUseDefault={chatRecord().isNonMember()}
                          ignoreDefaultClasses
                          disableBorder
                          width={35}
                          height={35}
                      />
                  </Suspense>
              }

              {/* Timestamp */}
              {(!displayUsername() || chatRecord().isSystem) && <Timestamp/>}

              <VStack alignItems={"flex-start"} width="100%">
                {/* Chat Username */}
                {(!isEmpty(chatRecord().displayName) && displayUsername()) &&
                    <ChatMessageUsername fetchedUserAccount={user()}/>}

                {/* Chat Message */}
                <Box className={css.markDownContainer}>
                  <Switch fallback={<Box>{message()}</Box>}>
                    <Match when={isURLForOpenMeta() && message()}>
                      <ChatMessageEmbeddable url={message()} onLoaded={props.onLoaded}/>
                    </Match>
                    <Match
                      when={appSettingsService().settingSaved() && !appSettingsService().getSetting("CHAT_DISABLE_MARKDOWN")}>
                      <SolidMarkDownText text={message()}/>
                    </Match>
                  </Switch>
                </Box>

                {wasModified() && <Box color={"$neutral10"} fontSize={8}>(edited...)</Box>}
              </VStack>
            </HStack>
          </VStack>

          {/* Context Menu */}
          <Menu id={MENU_ID}>
            <Item disabled>{`User: ${chatRecord().usertag ?? chatRecord().username}`}</Item>
            <Item disabled>{`ID: ${chatRecord().id.substring(0, 20)}...`}</Item>
            <Item disabled>{`Date: ${chatRecord().timestamp.toLocaleString()}`}</Item>
            <Separator/>
            <Item onClick={() => {
              navigator.clipboard.writeText(chatRecord().message);
            }}>Copy Message</Item>
            <Item onClick={() => {
              navigator.clipboard.writeText(chatRecord().id);
            }}>Copy Message ID</Item>
            <Item onClick={() => {
              navigator.clipboard.writeText(window.getSelection()?.toString() || "");
            }}>Copy Text Selection</Item>

            {/* Report Message */}
            {canReportMessage() &&
                <>
                    <Separator/>
                    <Item onClick={() => {
                      // chatService.setChatMessageToHighlight(chatRecord().id);
                      // TODO
                      // reportService.reportMessage(appService().client().usertag, chatRecord()).finally(() => {
                      //   chatService.setChatMessageToHighlight(undefined);
                      // });
                    }}>Report Message</Item>
                </>
            }

            {/* Delete Message */}
            {canDeleteChatMessage() &&
                <>
                    <Separator/>
                    <Item onClick={async () => {
                      await deleteMessageByID(
                        chatRecord().id,
                        (id) => websocketService().emitServerCommand(["DeleteChatMessageById", id]),
                        chatService()
                      );
                    }}>Delete Message</Item>
                </>
            }

            {/* Delete All Messages */}
            {canDeleteChatMessageByUser() &&
                <>
                    <Separator/>
                    <Item onClick={async () => {
                      let ids = chatService().findMessagesBy(x => x.usertag == chatRecord().usertag).map(x => x.id);
                      chatService().setChatMessagesSetToBeDeleted(ids);
                      await triggerDeleteMessagesWarning(ids, () => {
                        websocketService().emitServerCommand(["DeleteChatMessageByUsertag", chatRecord().usertag]);
                      }, () => {
                        chatService().setChatMessagesSetToBeDeleted([]);
                      });
                    }}>Delete All Messages by: {chatRecord().usertag}</Item>
                </>
            }
          </Menu>
        </ApiUserRecordProvider>
      </Show>
    </div>
  </>);
};

export default ChatMessageItem;