import { Box, Button, Center } from "@hope-ui/solid";
import { isEmpty } from "lodash-es";
import { Component, createEffect, createSignal } from "solid-js";
import abcjs from "abcjs";
import { SheetMusicCategory } from "~/models/sheet-music-dbo.models";
import { COMMON } from "~/util/const.common";

type MetaText = {
  title?: string;
  artist?: string;
  album?: string;
  bpm?: number;
};

export interface AbcNotationDetectorProps {
  data?: string;
  category: string;
  onMetaDetected?: (meta: MetaText) => void;
  onCategoryChange?: (category: SheetMusicCategory) => void;
}

export const AbcNotationDetector: Component<AbcNotationDetectorProps> = (props) => {
  const [metaText, setMetaText] = createSignal<MetaText>();
  let abcFormContentElement!: HTMLDivElement;
  const lastCategory = String(props.category);

  createEffect(() => {
    if (!props.data) {
      setMetaText(undefined);
      return;
    }

    try {
      // Try to auto-select the category based on the file data
      let rendered = abcjs.renderAbc(abcFormContentElement, props.data, {
        showDebug: COMMON.IS_DEV_MODE ? ["grid"] : undefined,
      });

      if (!rendered || !rendered?.[0] || !rendered?.[0].lines?.length) {
        throw new Error("Invalid ABC Notation");
      }

      let notionObject = rendered[0];
      let meta = notionObject?.metaText;
      if (isEmpty(meta)) throw new Error("Invalid ABC Notation");

      const detectedMeta = {
        title: meta?.title,
        artist: meta?.composer,
        album: meta?.source,
        bpm: notionObject.getBpm(),
      };

      setMetaText(detectedMeta);
    } catch {
      props.onCategoryChange?.(lastCategory as SheetMusicCategory ?? SheetMusicCategory.VirtualPiano);
      setMetaText(undefined);
    }
  });

  const handleAutoFill = () => {
    const meta = metaText();
    if (!meta) return;

    props.onMetaDetected?.(meta);
    props.onCategoryChange?.(SheetMusicCategory.ABCMusicNotation);
  };

  return (
    <Center w="100%" mb="1rem">
      <Box display="none" ref={abcFormContentElement}></Box>
      {metaText() &&
        <Button
          fontSize="$sm"
          as="pre"
          background={"$primaryDark1"}
          textAlign={"center"}
          onMouseDown={handleAutoFill}
        >
          ABC Notation detected. <br />Click to autofill
        </Button>
      }
    </Center>
  );
};
