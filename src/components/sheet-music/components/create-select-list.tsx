import { Box, HStack, Select, SelectContent, SelectListbox, SelectOption, SelectOptionIndicator, SelectOptionText, SelectPlaceholder, SelectTrigger, SelectValue } from "@hope-ui/solid";
import { FaSolidX } from "solid-icons/fa";
import { Accessor, Component, createEffect, createSignal, For } from "solid-js";

export type SelectListItem = {
  value: string;
  label?: string;
};

export interface CreateSelectListProps {
  defaultValue?: string | string[];
  valueSignalChange?: Accessor<string | string[] | undefined>;
  placeholder: string;
  isMultiple?: boolean;
  invalid?: boolean;
  data: SelectListItem[];
  onSetValue?: (values: string[] | string) => void;
}

export const CreateSelectList: Component<CreateSelectListProps> = (props) => {
  const [value, setValue] = createSignal(props.defaultValue || (props.isMultiple ? [] : ""));

  const onSetValue = (value: string | string[]) => {
    setValue(value);
    props.onSetValue?.(value);
  };

  createEffect(() => {
    let change = props.valueSignalChange?.();
    if (change) setValue(change);
  });

  return (
    <Select
      defaultValue={props.defaultValue}
      multiple={props.isMultiple}
      value={value()}
      onChange={onSetValue}
      invalid={props.invalid}
    >
      <SelectTrigger>
        <SelectPlaceholder>{props.placeholder}</SelectPlaceholder>
        <HStack spacing={"$2"}>
          <SelectValue>
            {item =>
              <For each={item.selectedOptions}>
                {option =>
                  <HStack 
                    spacing={"$0_5"}
                    background={"$primary1"}
                    padding={"$0_5 $1"}
                    borderRadius={"$sm"}
                  >
                    <Box
                      __tooltip_title={
                        props.data.find(x => x.value == option.value)?.label
                      }
                    >
                      {option.textValue}
                    </Box>
                    {props.isMultiple &&
                      <Box
                        onClick={() => {
                          if (!value() || typeof value() == "string") return;
                          //@ts-ignore
                          onSetValue(value().filter(x => x != option.value));
                        }}
                        _hover={{ "background": "$primary5" }}
                      >
                        <FaSolidX
                          width={"100%"}
                          height={"100%"}
                          font-size="0.7em"
                          style={{ "margin-top": "2px" }}
                        />
                      </Box>
                    }
                  </HStack>
                }
              </For>
            }
          </SelectValue>
        </HStack>
      </SelectTrigger>
      <SelectContent>
        <SelectListbox>
          <For each={props.data}>
            {item => (
              <SelectOption value={item.value}>
                <SelectOptionText>{item.value}</SelectOptionText>
                <SelectOptionIndicator />
              </SelectOption>
            )}
          </For>
        </SelectListbox>
      </SelectContent>
    </Select>
  );
};
