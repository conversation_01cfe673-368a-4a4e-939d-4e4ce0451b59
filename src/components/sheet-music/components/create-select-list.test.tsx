import { render, screen, fireEvent } from '@solidjs/testing-library';
import { describe, it, expect, vi } from 'vitest';
import { HopeProvider } from '@hope-ui/solid';
import { createRoot, createSignal } from 'solid-js';
import { CreateSelectList, SelectListItem } from './create-select-list';
import ThemeConfig from '~/util/theme-config';

const TestWrapper = (props: any) => (
  <HopeProvider config={ThemeConfig}>
    {props.children}
  </HopeProvider>
);

const renderWithRoot = (component: () => any) => {
  let dispose: (() => void) | undefined;
  const result = createRoot((disposeFn) => {
    dispose = disposeFn;
    return render(component);
  });
  return { ...result, dispose };
};

describe('CreateSelectList', () => {
  const mockData: SelectListItem[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' }
  ];

  it('should render with placeholder', () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
        />
      </TestWrapper>
    ));

    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('should render with default value', () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          defaultValue="option2"
        />
      </TestWrapper>
    ));

    // The selected value should be displayed
    expect(screen.getByText('option2')).toBeInTheDocument();
  });

  it('should call onSetValue when selection changes', async () => {
    const onSetValue = vi.fn();

    renderWithRoot(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Click to open the select (Hope UI Select uses combobox role)
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);

    // Wait for options to appear and click one
    await vi.waitFor(() => {
      const option = screen.getByText('option1');
      fireEvent.click(option);
    });

    expect(onSetValue).toHaveBeenCalledWith('option1');
  });

  it('should handle multiple selection', () => {
    const onSetValue = vi.fn();
    
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select options"
          isMultiple={true}
          defaultValue={['option1', 'option2']}
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Both selected values should be displayed
    expect(screen.getByText('option1')).toBeInTheDocument();
    expect(screen.getByText('option2')).toBeInTheDocument();
  });

  it('should show invalid state when invalid prop is true', () => {
    renderWithRoot(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          invalid={true}
        />
      </TestWrapper>
    ));

    // The select should have invalid styling (Hope UI uses combobox role)
    const select = screen.getByRole('combobox');
    expect(select).toHaveAttribute('aria-invalid', 'true');
  });

  it('should render selected options with their values', async () => {
    renderWithRoot(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          defaultValue="option1"
        />
      </TestWrapper>
    ));

    // The selected option should be displayed
    const selectedOption = screen.getByText('option1');
    expect(selectedOption).toBeInTheDocument();

    // The component should have the correct data structure
    expect(selectedOption).toHaveTextContent('option1');
  });

  it('should allow removing items in multiple selection mode', async () => {
    const onSetValue = vi.fn();

    renderWithRoot(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select options"
          isMultiple={true}
          defaultValue={['option1', 'option2']}
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Find the X icon for removing option1 (it's in a div with hover styling)
    const removeIcon = screen.getByText('option1').parentElement?.querySelector('svg');
    expect(removeIcon).toBeInTheDocument();

    if (removeIcon?.parentElement) {
      fireEvent.click(removeIcon.parentElement);
      expect(onSetValue).toHaveBeenCalledWith(['option2']);
    }
  });

  it('should update value when valueSignalChange prop changes', async () => {
    const [signalValue, setSignalValue] = createSignal('option1');

    renderWithRoot(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
          valueSignalChange={signalValue}
        />
      </TestWrapper>
    ));

    expect(screen.getByText('option1')).toBeInTheDocument();

    // Change the signal value
    setSignalValue('option2');

    // Wait for the effect to update using vi.waitFor
    await vi.waitFor(() => {
      expect(screen.getByText('option2')).toBeInTheDocument();
    });
  });

  it('should render all options in the dropdown', async () => {
    renderWithRoot(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select an option"
        />
      </TestWrapper>
    ));

    // Click to open the select (Hope UI uses combobox role)
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);

    // All options should be available
    await vi.waitFor(() => {
      expect(screen.getByText('option1')).toBeInTheDocument();
      expect(screen.getByText('option2')).toBeInTheDocument();
      expect(screen.getByText('option3')).toBeInTheDocument();
    });
  });

  it('should handle empty data array', () => {
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={[]}
          placeholder="No options available"
        />
      </TestWrapper>
    ));

    expect(screen.getByText('No options available')).toBeInTheDocument();
  });

  it('should initialize with empty array for multiple selection', () => {
    const onSetValue = vi.fn();
    
    render(() => (
      <TestWrapper>
        <CreateSelectList
          data={mockData}
          placeholder="Select options"
          isMultiple={true}
          onSetValue={onSetValue}
        />
      </TestWrapper>
    ));

    // Should start with empty selection
    expect(screen.getByText('Select options')).toBeInTheDocument();
  });
});
