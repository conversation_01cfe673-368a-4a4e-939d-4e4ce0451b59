import { render, screen, fireEvent, waitFor } from '@solidjs/testing-library';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { HopeProvider } from '@hope-ui/solid';
import { useService } from 'solid-services';
import { createRoot } from 'solid-js';
import SheetMusicUpload from './sheet-music-upload';
import {
  MockAppService,
  MockDisplaysService,
  MockSheetMusicService,
  MockSoundEffectsService
} from '../../../tests/mocks/service.mocks';
import { SheetMusicCategory, SheetMusicDifficultyLevel } from '~/models/sheet-music-dbo.models';
import AppService from '~/services/app.service';
import DisplaysService from '~/services/displays.service';
import { SheetMusicService } from '~/services/sheet-music.service';
import SoundEffectsService from '~/services/sound-effects.service';
import ThemeConfig from '~/util/theme-config';

// Mock dependencies
vi.mock('solid-services', () => ({
  useService: vi.fn()
}));

vi.mock('~/util/sweetalert', () => ({
  default: vi.fn(() => ({
    fire: vi.fn().mockImplementation(async (options: any) => {
      // Simulate the preConfirm function being called
      if (options.preConfirm) {
        try {
          await options.preConfirm();
        } catch (error) {
          // Handle preConfirm errors
        }
      }
      return { isConfirmed: true };
    }),
    close: vi.fn()
  }))
}));

vi.mock('~/services/notification.service', () => ({
  default: {
    show: vi.fn()
  }
}));

vi.mock('../midi-player/midi-vp-sequencer-ui', () => ({
  default: () => <div data-testid="vp-sequencer">VP Sequencer</div>
}));

vi.mock('./sheet-music-common', () => ({
  SheetMusicContentUpload: (props: any) => (
    <div data-testid="sheet-music-content-upload">
      <button onClick={() => props.onNewBodyContentUpdate?.('test-data')}>
        Update Content
      </button>
      <button onClick={() => props.onLoad?.({ data: btoa('loaded-data') })}>
        Load Data
      </button>
    </div>
  )
}));

vi.mock('../motion/motion.fade-in', () => ({
  default: (props: any) => <div>{props.children}</div>
}));

vi.mock('abcjs', () => ({
  default: {
    renderAbc: vi.fn()
  }
}));

const TestWrapper = (props: any) => (
  <HopeProvider config={ThemeConfig}>
    {props.children}
  </HopeProvider>
);

const renderWithRoot = (component: () => any) => {
  let dispose: () => void;
  const result = createRoot((d) => {
    dispose = d;
    return render(() => (
      <TestWrapper>
        {component()}
      </TestWrapper>
    ));
  });
  return { ...result, dispose: () => dispose?.() };
};

describe('SheetMusicUpload', () => {
  let mockAppService: ReturnType<typeof MockAppService>;
  let mockDisplaysService: ReturnType<typeof MockDisplaysService>;
  let mockSheetMusicService: ReturnType<typeof MockSheetMusicService>;
  let mockSoundEffectsService: ReturnType<typeof MockSoundEffectsService>;

  beforeEach(() => {
    vi.clearAllMocks();

    mockAppService = MockAppService();
    mockDisplaysService = MockDisplaysService();
    mockSheetMusicService = MockSheetMusicService();
    mockSoundEffectsService = MockSoundEffectsService();

    // Setup default mock returns
    mockDisplaysService.getDisplay = vi.fn().mockReturnValue(true);
    mockSheetMusicService.editMode = vi.fn().mockReturnValue(false);
    mockSheetMusicService.activeViewerData = vi.fn().mockReturnValue(undefined);
    mockSheetMusicService.newSheetFromViewerUploadData = vi.fn().mockReturnValue(undefined);

    (useService as any).mockImplementation((service: any) => {
      if (service === AppService) return () => mockAppService;
      if (service === DisplaysService) return () => mockDisplaysService;
      if (service === SheetMusicService) return () => mockSheetMusicService;
      if (service === SoundEffectsService) return () => mockSoundEffectsService;
      return () => ({});
    });
  });

  it('should render upload modal with correct title', () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    expect(screen.getByText(/Sheet Music - New Upload/)).toBeInTheDocument();

    dispose();
  });

  it('should render edit modal with correct title when in edit mode', () => {
    mockSheetMusicService.editMode = vi.fn().mockReturnValue(true);

    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    expect(screen.getByText(/Sheet Music - Edit/)).toBeInTheDocument();

    dispose();
  });

  it('should render all form fields', () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    expect(screen.getByPlaceholderText('title')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('original artist')).toBeInTheDocument();
    expect(screen.getByPlaceholderText("original artist's album")).toBeInTheDocument();
    expect(screen.getByPlaceholderText('tempo/bpm')).toBeInTheDocument();
    expect(screen.getByText('Choose some tags...')).toBeInTheDocument();
    // Note: The actual text might be different, let's check what's actually rendered
    expect(screen.getByText(/category/i)).toBeInTheDocument();
    expect(screen.getByText(/difficulty/i)).toBeInTheDocument();
    expect(screen.getByText(/privacy/i)).toBeInTheDocument();

    dispose();
  });

  it('should disable submit button when form is invalid', async () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    const submitButton = screen.getByText('Submit');
    expect(submitButton).toBeDisabled();

    dispose();
  });

  it('should enable submit button when form is valid', async () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText('title');
    fireEvent.input(titleInput, { target: { value: 'Test Song' } });

    // Simulate data being loaded
    const loadButton = screen.getByText('Load Data');
    fireEvent.click(loadButton);

    // Wait for validation
    await waitFor(() => {
      const submitButton = screen.getByText('Submit');
      expect(submitButton).not.toBeDisabled();
    }, { timeout: 3000 });

    dispose();
  });

  it('should update form fields when user inputs data', () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    const titleInput = screen.getByPlaceholderText('title');
    const artistInput = screen.getByPlaceholderText('original artist');
    const tempoInput = screen.getByPlaceholderText('tempo/bpm');

    fireEvent.input(titleInput, { target: { value: 'My Song' } });
    fireEvent.input(artistInput, { target: { value: 'My Artist' } });
    fireEvent.input(tempoInput, { target: { value: 140 } });

    expect(titleInput).toHaveValue('My Song');
    expect(artistInput).toHaveValue('My Artist');
    expect(tempoInput).toHaveValue(140);

    dispose();
  });

  it('should show VP sequencer when category is VirtualPiano and data exists', async () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Load some data first
    const loadButton = screen.getByText('Load Data');
    fireEvent.click(loadButton);

    // VP sequencer should be visible by default (VirtualPiano category)
    await waitFor(() => {
      expect(screen.getByTestId('vp-sequencer')).toBeInTheDocument();
    });

    dispose();
  });

  it('should populate form with existing data in edit mode', () => {
    const mockActiveData = {
      data: {
        id: 'test-id',
        title: 'Existing Song',
        songArtist: 'Existing Artist',
        songAlbum: 'Existing Album',
        tags: ['Classical', 'Piano'],
        description: 'Test description',
        category: SheetMusicCategory.ABCMusicNotation,
        privacyStatus: 'Private',
        difficultyLevel: SheetMusicDifficultyLevel.Advanced,
        bpm: 150
      },
      file: {
        data: new TextEncoder().encode('existing-data')
      }
    };

    mockSheetMusicService.editMode = vi.fn().mockReturnValue(true);
    mockSheetMusicService.activeViewerData = vi.fn().mockReturnValue(mockActiveData);

    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    expect(screen.getByDisplayValue('Existing Song')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Existing Artist')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Existing Album')).toBeInTheDocument();
    expect(screen.getByDisplayValue('150')).toBeInTheDocument();

    dispose();
  });

  it('should call upload service when submitting new sheet music', async () => {
    mockSheetMusicService.uploadSheetMusic = vi.fn().mockResolvedValue({ id: 'new-id' });

    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText('title');
    fireEvent.input(titleInput, { target: { value: 'Test Song' } });

    const loadButton = screen.getByText('Load Data');
    fireEvent.click(loadButton);

    // Wait for form to be valid and submit
    await waitFor(async () => {
      const submitButton = screen.getByText('Submit');
      expect(submitButton).not.toBeDisabled();
      fireEvent.click(submitButton);
    }, { timeout: 5000 });

    // Wait a bit for the SweetAlert confirmation to be triggered
    await waitFor(() => {
      expect(mockSheetMusicService.uploadSheetMusic).toHaveBeenCalled();
    }, { timeout: 5000 });

    dispose();
  });

  it('should call update service when submitting in edit mode', async () => {
    const mockActiveData = {
      data: {
        id: 'test-id',
        title: 'Existing Song',
        category: SheetMusicCategory.VirtualPiano,
        privacyStatus: 'Public',
        difficultyLevel: SheetMusicDifficultyLevel.Unknown,
        bpm: 120
      },
      file: {
        data: new TextEncoder().encode('existing-data')
      }
    };

    mockSheetMusicService.editMode = vi.fn().mockReturnValue(true);
    mockSheetMusicService.activeViewerData = vi.fn().mockReturnValue(mockActiveData);
    mockSheetMusicService.updateSheetMusic = vi.fn().mockResolvedValue({ id: 'test-id' });

    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Make a change to enable submit
    const titleInput = screen.getByDisplayValue('Existing Song');
    fireEvent.input(titleInput, { target: { value: 'Modified Song' } });

    // Wait for form to detect changes and submit
    await waitFor(async () => {
      const submitButton = screen.getByText('Submit');
      expect(submitButton).not.toBeDisabled();
      fireEvent.click(submitButton);
    }, { timeout: 5000 });

    await waitFor(() => {
      expect(mockSheetMusicService.updateSheetMusic).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Modified Song'
        }),
        'test-id'
      );
    }, { timeout: 5000 });

    dispose();
  });

  it('should close modal when cancel button is clicked', () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockDisplaysService.setDisplay).toHaveBeenCalledWith('SHEET_MUSIC_UPLOAD_MODAL', false);
    expect(mockSheetMusicService.setEditMode).toHaveBeenCalledWith(false);
    expect(mockSheetMusicService.setActiveViewerData).toHaveBeenCalledWith(undefined);

    dispose();
  });

  it('should handle content upload updates', () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    const updateButton = screen.getByText('Update Content');
    fireEvent.click(updateButton);

    // The form should now have the updated data
    // This would be verified through the form validation state

    dispose();
  });

  it('should show ABC notation detector for non-ABC categories', () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // ABC detector should be present (since default category is VirtualPiano, not ABC)
    expect(screen.getByTestId('sheet-music-content-upload')).toBeInTheDocument();

    dispose();
  });

  it('should validate field constraints', async () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Test title length constraint
    const titleInput = screen.getByPlaceholderText('title');
    fireEvent.input(titleInput, { target: { value: 'a'.repeat(200) } }); // Exceeds max length

    // Test tempo range constraint
    const tempoInput = screen.getByPlaceholderText('tempo/bpm');
    fireEvent.input(tempoInput, { target: { value: '0' } }); // Below minimum

    // Submit should be disabled due to validation errors
    await waitFor(() => {
      const submitButton = screen.getByText('Submit');
      expect(submitButton).toBeDisabled();
    });

    dispose();
  });

  it('should handle upload errors gracefully', async () => {
    mockSheetMusicService.uploadSheetMusic = vi.fn().mockRejectedValue(
      new Error('Upload failed')
    );

    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText('title');
    fireEvent.input(titleInput, { target: { value: 'Test Song' } });

    const loadButton = screen.getByText('Load Data');
    fireEvent.click(loadButton);

    // Submit and expect error handling
    await waitFor(async () => {
      const submitButton = screen.getByText('Submit');
      expect(submitButton).not.toBeDisabled();
      fireEvent.click(submitButton);
    }, { timeout: 5000 });

    // Error should be handled (SweetAlert would show error)
    await waitFor(() => {
      expect(mockSheetMusicService.uploadSheetMusic).toHaveBeenCalled();
    }, { timeout: 5000 });

    dispose();
  });

  it('should not allow submit when no changes in edit mode', () => {
    const mockActiveData = {
      data: {
        id: 'test-id',
        title: 'Existing Song',
        category: SheetMusicCategory.VirtualPiano,
        privacyStatus: 'Public',
        difficultyLevel: SheetMusicDifficultyLevel.Unknown,
        bpm: 120,
        tags: []
      },
      file: {
        data: new TextEncoder().encode('existing-data')
      }
    };

    mockSheetMusicService.editMode = vi.fn().mockReturnValue(true);
    mockSheetMusicService.activeViewerData = vi.fn().mockReturnValue(mockActiveData);

    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Submit should be disabled when no changes are made
    const submitButton = screen.getByText('Submit');
    expect(submitButton).toBeDisabled();

    dispose();
  });

  it('should handle different privacy levels', async () => {
    const { dispose } = renderWithRoot(() => <SheetMusicUpload />);

    // Fill required fields
    const titleInput = screen.getByPlaceholderText('title');
    fireEvent.input(titleInput, { target: { value: 'Test Song' } });

    const loadButton = screen.getByText('Load Data');
    fireEvent.click(loadButton);

    // Test different privacy levels would affect the submit message
    // This would be tested through the SweetAlert mock
    await waitFor(() => {
      const submitButton = screen.getByText('Submit');
      expect(submitButton).not.toBeDisabled();
    });

    dispose();
  });

  it('should cleanup on unmount', () => {
    const { unmount } = render(() => (
      <TestWrapper>
        <SheetMusicUpload />
      </TestWrapper>
    ));

    unmount();

    // Cleanup should have been called
    expect(mockSheetMusicService.setNewSheetFromViewerUploadData).toHaveBeenCalledWith({ data: undefined });
  });
});
