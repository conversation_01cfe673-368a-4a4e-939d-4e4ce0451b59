import { Box, Button, Divider, Input, VStack } from "@hope-ui/solid";
import { css } from "@stitches/core";
import abcjs from "abcjs";
import { FaSolidFileArrowUp } from "solid-icons/fa";
import { Component, createEffect, createSignal, Match, onMount, Switch } from "solid-js";
import { useService } from "solid-services";
import { SheetMusicCategory } from "~/models/sheet-music-dbo.models";
import MidiPlayerService from "~/services/midi-player.service";
import { removeBase64Header } from "~/util/helpers";
import MidiLyricsDisplay from "../midi-player/midi-lyrics-display";
import { AbcNotationFormContent } from "./sheet-music-details";

export type SheetMusicDragNDropInputOnLoad = {
  data: string | ArrayBuffer;
  fileName: string;
  fileType: string;
};

type SheetMusicContentUploadProps = {
  onLoad?: (output: SheetMusicDragNDropInputOnLoad) => void;
  onCreateNewPressed?: () => void;
  onNewBodyContentUpdate?: (text: string) => void;
  value?: string | null;
  allowCreateNew?: boolean;
  displayDataAfterUpload?: boolean;
  isVPSheet?: boolean;
  categoryType?: SheetMusicCategory;
  acceptedTypes?: string;
  previewBodyBackground?: string;
  previewBodyTextColor?: string;
};

const bodyPreviewContainerCSS = css({
  "--focus-color": "transparent",
  overflowY: "scroll",
  overflowX: "hidden",
  height: 420,
  padding: 5,
  borderRadius: 5,
  width: "100%",
  border: "3px solid transparent",
  outline: "none",
  transition: "all 250ms ease",
  userSelect: "none",
  ".plain-text-file": {
    outline: "none",
    padding: 5
  },
  "[contenteditable][placeholder]:empty:before": {
    content: "attr(placeholder)",
    color: "lightgray",
    backgroundColor: "transparent"
  },
  "&:focus-within": {
    "--focus-color": "var(--hope-colors-accent1)"
  }
});

export const SheetMusicContentUpload: Component<SheetMusicContentUploadProps> = (props) => {
  const DEFAULT_HEADER = `Drag & Drop your Sheet Music Here`;

  const midiPlayerService = useService(MidiPlayerService);
  const [loading, setIsLoading] = createSignal(false);
  const [isNewFile, setIsNewFile] = createSignal(false);
  const [newContentInputFocused, setNewContentInputFocused] = createSignal(false);
  const [formContent, setFormContent] = createSignal<string>();
  const [headerText, setHeaderText] = createSignal(DEFAULT_HEADER);
  let dragAreaRef!: HTMLDivElement;
  let abcFormContentElement!: HTMLDivElement;
  let inputElement!: HTMLInputElement;

  const onLoadFile = (file: File) => {
    setIsLoading(true);

    let reader = new FileReader();
    reader.onload = (ev) => {
      let data: SheetMusicDragNDropInputOnLoad = {
        fileType: file.type,
        fileName: file.name,
        data: removeBase64Header(ev.target?.result as any) ?? ""
      };

      if (props.onLoad) props.onLoad(data);
      if (props.displayDataAfterUpload) {
        setFormContent(atob(data.data as string));
        setIsNewFile(true);
      }

      setIsLoading(false);
    };
    reader.readAsDataURL(file);
  };

  const onInput = (evt: InputEvent | ClipboardEvent) => {
    let text = (evt.target as HTMLElement).innerText;
    if (props.onNewBodyContentUpdate) props.onNewBodyContentUpdate(text);
  };

  onMount(() => {
    if (!props.value) return;
    setIsNewFile(true);
    setFormContent(props.value);
  });

  createEffect(() => {
    if (props.value === null) {
      setIsNewFile(false);
      setFormContent(undefined);
    }
  });

  const FormContent = () => {
    onMount(() => {
      setFormContent(props.value ?? undefined);
    });

    return (<Box
      class="plain-text-file"
      cursor={"text"}
      placeHolder="Click here to start..."
      fontFamily={"'Verdana', 'Georgia', serif !important"}
      onFocus={(_) => setNewContentInputFocused(true)}
      onBlur={(_) => setNewContentInputFocused(false)}
      onKeyDown={(evt) => evt.stopPropagation()}
      onInput={onInput}
      onPaste={onInput}
      //@ts-ignore
      contentEditable={"plaintext-only"}
    >
      {formContent()}
    </Box>);
  };

  const isAbcNotation = () => props.categoryType == SheetMusicCategory.ABCMusicNotation;

  return (<>
    <Switch>
      <Match when={isNewFile()}>
        <Box
          class={bodyPreviewContainerCSS()}
          height={props.isVPSheet ? "390px" : "420px"}
          border={newContentInputFocused() ? "2px solid var(--hope-colors-accent1)" : ""}
          background={props.previewBodyBackground || "white"}
          color={props.previewBodyTextColor || "black"}
        >
          <Switch fallback={<FormContent/>}>
            <Match when={(props.isVPSheet && midiPlayerService().midiSequencerState.lyrics || []).length > 0}>
              <Box
                overflowY={"scroll"}
                color={"$neutral11 !important"}
              >
                <MidiLyricsDisplay
                  lyrics={midiPlayerService().midiSequencerState.lyrics ?? []}/>
              </Box>
            </Match>
            <Match when={isAbcNotation()}>
              <AbcNotationFormContent
                data={props.value ?? ""}
                rawEdit={<FormContent/>}
              />
            </Match>
          </Switch>
        </Box>
      </Match>

      <Match when={!isNewFile() && !formContent()}>
        <Box w="100%" h="100%"
             border={"2px dashed white"}
             borderRadius={5}
             display="flex"
             alignItems={"center"}
             justifyContent={"center"}
             padding={15}
             ref={dragAreaRef}

             onDragOver={(evt: DragEvent) => {
               evt.preventDefault();
               setHeaderText("Release to upload file!");
             }}

             onDragLeave={(evt: DragEvent) => {
               setHeaderText(DEFAULT_HEADER);
             }}

             onDrop={(evt: DragEvent) => {
               evt.preventDefault();
               setHeaderText(DEFAULT_HEADER);
               let file = evt.dataTransfer?.files[0];
               if (file) onLoadFile(file);
             }}
        >
          <VStack textAlign={"center"} spacing="$2">
            <FaSolidFileArrowUp size={48}/>
            <Box>{headerText()}</Box>
            <Box>{"OR"}</Box>
            <Button
              background={"$primaryDark1"}
              onclick={() => {
                if (inputElement) inputElement.click();
              }}>Browse File</Button>

            {props.allowCreateNew &&
                <>
                    <Box>{"OR"}</Box>
                    <Button background={"$primaryDark1"}
                            onClick={() => {
                              setIsNewFile(true);
                              // setFormContent("");
                              if (props.onCreateNewPressed) props.onCreateNewPressed();
                            }}>Create New</Button>
                </>
            }
          </VStack>
        </Box>
        <Input
          ref={inputElement}
          className={"hidden-file-input"}
          pointerEvents="none"
          accept={props.acceptedTypes || "text/plain"}
          type="file"
          onChange={() => {
            let input = inputElement;
            if (input != null) {
              let file = input.files?.[0];
              if ((input.files?.length || 0) > 0 && file != null) {
                onLoadFile(file);
              }
            }
          }}
        />
      </Match>
    </Switch>
  </>);
};
