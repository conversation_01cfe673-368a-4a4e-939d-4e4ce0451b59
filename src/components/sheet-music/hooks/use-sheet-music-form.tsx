import { cloneDeep, isEqual } from "lodash-es";
import { createEffect, createMemo, createSignal } from "solid-js";
import { createStore } from "solid-js/store";
import { z } from "zod";
import { SheetMusicCategory, SheetMusicDifficultyLevel, SheetMusicRequest } from "~/models/sheet-music-dbo.models";

const DEFAULT_DATA_UPLOAD: SheetMusicRequest = {
  title: "",
  songArtist: undefined,
  songAlbum: undefined,
  tags: [],
  description: undefined,
  category: SheetMusicCategory.VirtualPiano,
  privacy: "Public",
  difficultyLevel: SheetMusicDifficultyLevel.Unknown,
  data: undefined,
  tempo: 120,
  totalTime: undefined,
};

const sheetMusicDataSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  data: z.string().min(1, { message: "Data is required" }),
  songArtist: z.string().max(1000).optional().nullable(),
  songAlbum: z.string().max(1000).optional().nullable(),
  tags: z.array(z.string()).max(10).default([]),
  description: z.string().max(1000).optional().nullable(),
  category: z.nativeEnum(SheetMusicCategory).default(DEFAULT_DATA_UPLOAD.category as SheetMusicCategory),
  difficultyLevel: z.nativeEnum(SheetMusicDifficultyLevel).default(DEFAULT_DATA_UPLOAD.difficultyLevel as SheetMusicDifficultyLevel),
  privacy: z.enum(["Public", "Unlisted", "Private"]).default(DEFAULT_DATA_UPLOAD.privacy ?? "Public"),
  tempo: z.number().min(1, { message: "Tempo must be at least 1" }).max(1000, { message: "Tempo must be at most 1000" }).default(DEFAULT_DATA_UPLOAD.tempo as number),
});

export type ValidationErrors = Record<string, string>;

export interface UseSheetMusicFormOptions {
  initialData?: SheetMusicRequest;
  isEditMode?: boolean;
}

export function useSheetMusicForm(options: UseSheetMusicFormOptions = {}) {
  const [fields, setFields] = createStore<SheetMusicRequest>({ 
    ...DEFAULT_DATA_UPLOAD,
    ...options.initialData 
  });
  
  const [initialData, setInitialData] = createSignal<SheetMusicRequest | undefined>(
    options.initialData ? cloneDeep(options.initialData) : undefined
  );
  
  const [validationErrors, setValidationErrors] = createSignal<ValidationErrors>({});
  const [isValidating, setIsValidating] = createSignal(false);

  // Computed values
  const isValid = createMemo(() => Object.keys(validationErrors()).length === 0);
  
  const hasChanges = createMemo(() => {
    if (!options.isEditMode || !initialData()) return true;
    
    const initial = initialData()!;
    const current = fields;
    
    // Compare all relevant fields
    return !isEqual(
      {
        title: initial.title,
        songArtist: initial.songArtist,
        songAlbum: initial.songAlbum,
        tags: initial.tags || [],
        description: initial.description,
        category: initial.category,
        privacy: initial.privacy,
        difficultyLevel: initial.difficultyLevel,
        tempo: initial.tempo,
        data: initial.data,
      },
      {
        title: current.title,
        songArtist: current.songArtist,
        songAlbum: current.songAlbum,
        tags: current.tags || [],
        description: current.description,
        category: current.category,
        privacy: current.privacy,
        difficultyLevel: current.difficultyLevel,
        tempo: current.tempo,
        data: current.data,
      }
    );
  });

  const canSubmit = createMemo(() => isValid() && hasChanges());

  // Validation effect
  createEffect(() => {
    setIsValidating(true);

    try {
      const result = sheetMusicDataSchema.safeParse(fields);

      if (!result.success) {
        const errors: ValidationErrors = {};
        result.error.issues.forEach(issue => {
          const path = issue.path.join('.');
          errors[path] = issue.message;
        });
        setValidationErrors(errors);
      } else {
        setValidationErrors({});
      }

    } catch (error) {
      console.error('Validation error:', error);
      setValidationErrors({ general: 'Validation failed' });
    } finally {
      setIsValidating(false);
    }
  });

  // Helper functions
  const updateField = <K extends keyof SheetMusicRequest>(
    field: K, 
    value: SheetMusicRequest[K]
  ) => {
    setFields(field, value);
  };

  const updateFields = (updates: Partial<SheetMusicRequest>) => {
    Object.entries(updates).forEach(([key, value]) => {
      setFields(key as keyof SheetMusicRequest, value);
    });
  };

  const resetForm = () => {
    setFields({ ...DEFAULT_DATA_UPLOAD });
    setValidationErrors({});
  };

  const setInitialFormData = (data: SheetMusicRequest) => {
    setInitialData(cloneDeep(data));
    setFields({ ...data });
  };

  const validateField = (field: keyof SheetMusicRequest): string | undefined => {
    return validationErrors()[field];
  };

  const getFormData = async (): Promise<SheetMusicRequest> => {
    return await sheetMusicDataSchema.parseAsync(fields) as SheetMusicRequest;
  };

  return {
    fields,
    get validationErrors() { return validationErrors(); },
    get isValid() { return isValid(); },
    get hasChanges() { return hasChanges(); },
    get canSubmit() { return canSubmit(); },
    get isValidating() { return isValidating(); },
    updateField,
    updateFields,
    resetForm,
    setInitialFormData,
    validateField,
    getFormData,
    schema: sheetMusicDataSchema,
  };
}
