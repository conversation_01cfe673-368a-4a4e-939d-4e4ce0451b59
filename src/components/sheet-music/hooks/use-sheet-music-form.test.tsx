import { describe, it, expect, beforeEach } from 'vitest';
import { createRoot } from 'solid-js';
import { useSheetMusicForm } from './use-sheet-music-form';
import { SheetMusicCategory, SheetMusicDifficultyLevel, SheetMusicRequest } from '~/models/sheet-music-dbo.models';
import { testEffect, waitFor } from "@solidjs/testing-library";

describe('useSheetMusicForm', () => {
  let dispose: () => void;
  let form: ReturnType<typeof useSheetMusicForm>;

  beforeEach(() => {
    createRoot((d) => {
      dispose = d;
      form = useSheetMusicForm();
    });
  });

  afterEach(() => {
    dispose?.();
  });

  describe('initialization', () => {
    it('should initialize with default values', () => {
      expect(form.fields.title).toBe('');
      expect(form.fields.category).toBe(SheetMusicCategory.VirtualPiano);
      expect(form.fields.privacy).toBe('Public');
      expect(form.fields.difficultyLevel).toBe(SheetMusicDifficultyLevel.Unknown);
      expect(form.fields.tempo).toBe(120);
      expect(form.fields.tags).toEqual([]);
    });

    it('should initialize with provided initial data', () => {
      createRoot((d) => {
        dispose = d;
        const initialData: SheetMusicRequest = {
          title: 'Test Song',
          songArtist: 'Test Artist',
          category: SheetMusicCategory.ABCMusicNotation,
          privacy: 'Private',
          tempo: 140,
          tags: ['Classical', 'Piano'],
          data: 'test-data'
        };

        form = useSheetMusicForm({ initialData });

        expect(form.fields.title).toBe('Test Song');
        expect(form.fields.songArtist).toBe('Test Artist');
        expect(form.fields.category).toBe(SheetMusicCategory.ABCMusicNotation);
        expect(form.fields.privacy).toBe('Private');
        expect(form.fields.tempo).toBe(140);
        expect(form.fields.tags).toEqual(['Classical', 'Piano']);
        expect(form.fields.data).toBe('test-data');
      });
    });
  });

  describe('field updates', () => {
    it('should update individual fields', () => {
      form.updateField('title', 'New Title');
      expect(form.fields.title).toBe('New Title');

      form.updateField('tempo', 150);
      expect(form.fields.tempo).toBe(150);

      form.updateField('tags', ['Jazz', 'Blues']);
      expect(form.fields.tags).toEqual(['Jazz', 'Blues']);
    });

    it('should update multiple fields at once', () => {
      form.updateFields({
        title: 'Batch Update',
        songArtist: 'New Artist',
        tempo: 160
      });

      expect(form.fields.title).toBe('Batch Update');
      expect(form.fields.songArtist).toBe('New Artist');
      expect(form.fields.tempo).toBe(160);
    });
  });

  describe('validation', () => {
    it('should be invalid when required fields are missing', () => {
      // 2. Assert the final state.
      // The form is created with empty 'title' and 'data', so validation has already run.

      // `isValidating` should be false because the validation is complete.
      expect(form.isValidating).toBe(false);

      // The form should be invalid.
      expect(form.isValid).toBe(false);

      // 3. Check for specific field errors.
      // There should be error messages for the required fields.
      expect(form.validateField('title')).toBe('Title is required');
      expect(form.validateField('data')).toContain('Required');
    });

    it('should be valid when all required fields are provided', async () => {
      form.updateFields({
        title: 'Valid Title',
        data: 'valid-data'
      });

      // Wait for validation to complete
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(form.isValid).toBe(true);
      expect(form.validateField('title')).toBeFalsy();
      expect(form.validateField('data')).toBeFalsy();
    });

    it('should validate field length constraints', async () => {
      form.updateFields({
        title: 'Valid Title',
        data: 'valid-data',
        songArtist: 'a'.repeat(1001) // Exceeds max length
      });

      // Wait for validation to complete
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(form.isValid).toBe(false);
      expect(form.validateField('songArtist')).toBeTruthy();
    });

    it('should validate tempo range', async () => {
      form.updateFields({
        title: 'Valid Title',
        data: 'valid-data',
        tempo: 0 // Below minimum
      });

      expect(form.isValid).toBe(false);
      expect(form.validateField('tempo')).toBeTruthy();
    });
  });

  describe('change detection', () => {
    it('should detect changes in non-edit mode', () => {
      expect(form.hasChanges).toBe(true); // Always true in non-edit mode
    });

    it('should detect no changes when data matches initial data in edit mode', () => {
      const initialData: SheetMusicRequest = {
        title: 'Test Song',
        data: 'test-data',
        category: SheetMusicCategory.VirtualPiano,
        privacy: 'Public',
        difficultyLevel: SheetMusicDifficultyLevel.Unknown,
        tempo: 120,
        tags: []
      };

      createRoot((d) => {
        dispose = d;
        form = useSheetMusicForm({
          initialData,
          isEditMode: true
        });

        expect(form.hasChanges).toBe(false);
      });
    });

    it('should detect changes when data differs from initial data in edit mode', async () => {
      const initialData: SheetMusicRequest = {
        title: 'Test Song',
        data: 'test-data',
        category: SheetMusicCategory.VirtualPiano,
        privacy: 'Public',
        difficultyLevel: SheetMusicDifficultyLevel.Unknown,
        tempo: 120,
        tags: []
      };

      createRoot((d) => {
        dispose = d;
        form = useSheetMusicForm({
          initialData,
          isEditMode: true
        });

        // Wait a tick for the form to initialize
        setTimeout(() => {
          form.updateField('title', 'Changed Title');
          expect(form.hasChanges).toBe(true);
        }, 10);
      });
    });
  });

  describe('submit readiness', () => {
    it('should not be ready to submit when invalid', async () => {
      // Wait for validation
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(form.canSubmit).toBe(false);
    });

    it('should be ready to submit when valid and has changes', async () => {
      form.updateFields({
        title: 'Valid Title',
        data: 'valid-data'
      });

      // Wait for validation
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(form.canSubmit).toBe(true);
    });
  });

  describe('form data retrieval', () => {
    it('should return validated form data', async () => {
      form.updateFields({
        title: 'Test Title',
        data: 'test-data',
        tempo: 140
      });

      const formData = await form.getFormData();

      expect(formData.title).toBe('Test Title');
      expect(formData.data).toBe('test-data');
      expect(formData.tempo).toBe(140);
    });

    it('should throw error for invalid data', async () => {
      // Leave required fields empty
      await expect(form.getFormData()).rejects.toThrow();
    });
  });

  describe('form reset', () => {
    it('should reset form to default values', () => {
      form.updateFields({
        title: 'Test Title',
        songArtist: 'Test Artist',
        tempo: 140
      });

      form.resetForm();

      expect(form.fields.title).toBe('');
      expect(form.fields.songArtist).toBeUndefined();
      expect(form.fields.tempo).toBe(120);
    });
  });
});
