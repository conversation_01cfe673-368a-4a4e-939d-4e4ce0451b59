import { Box, Center, hope, HStack, Image, VStack } from "@hope-ui/solid";
import { Component, createSignal, For, JSXElement, onMount, Show } from "solid-js";
import { useService } from "solid-services";
import ResourceService from "~/services/resource.service";
import { IMAGES } from "~/util/const.common";
import css from "./css/search-ui.generic-display.module.sass";

export type GenericDisplayCardItem = {
  category?: string;
  categoryColor?: string;
  subCategory?: string;
  title: string;
  subtitle?: string;
  thumbnailImage?: string;
  avatarImage?: string;
  tooltip?: string;
  badges?: JSXElement[];
  onMount?: (element: HTMLDivElement) => void;
  onClick?: () => void;
};

const GenericDisplayCard: Component<{ item: GenericDisplayCardItem; }> = (props) => {
  const [thumbnail, setThumbnail] = createSignal<string>();
  const [avatarImage, setAvatarImage] = createSignal<string>();
  const resourceService = useService(ResourceService);

  onMount(async () => {
    const loadImage = async (imageUrl: string | undefined) => {
      const fallback = await resourceService().getDefaultProfileImage();
      if (!imageUrl) return fallback;
      try {
        const image = await resourceService().getServerProfileImage(imageUrl);
        return image || fallback;
      } catch {
        return fallback;
      }
    };

    setThumbnail(await resourceService().getAssetImage(props.item.thumbnailImage || IMAGES.DEFAULT_PROFILE_IMAGE));
    setAvatarImage(await loadImage(props.item.avatarImage));
  });

  function onRef(element: HTMLDivElement) {
    if (props.item.onMount) props.item.onMount(element);
  }

  return (<>
    <Box {...props}
      class={[css.card].join(" ")} ref={onRef}
      onClick={() => { if (props.item.onClick) props.item.onClick(); }}
    >
      <VStack w="100%" h={200} alignItems={"flex-start"} position={"relative"}>
        <Box class={css.cardThumbnailContainer} __tooltip_title={props.item.tooltip}>
          <Show when={props.item.category}
            fallback={
              <>
                <Box class={[css.category].join(" ")}
                  h={15} w="100%"
                  position={"absolute"} margin={"0 !important"} zIndex={1} background={"$primaryDark1"}
                />
                <Box class={[css.category].join(" ")}
                  h={15} w="100%"
                  bottom={0}
                  position={"absolute"} margin={"0 !important"} zIndex={1} background={"$primaryDark1"}
                />
              </>
            }
          >
            <Box
              class={css.category}
              color={`${props.item.categoryColor ?? "var(--hope-colors-accent1)"} !important` }
            >{props.item.category}</Box>
          </Show>

          <Show when={props.item.thumbnailImage && thumbnail()}
            fallback={
              <Box class={css.thumbnail} minWidth={179} h="80%">
                <Center h="100%">
                  PianoRhythm
                </Center>
              </Box>
            }
          >
            <Box class={css.thumbnail}>
              <Image
                loading="lazy"
                background="$primary1"
                src={thumbnail()}
              />
            </Box>
          </Show>
        </Box>

        {props.item.badges &&
          <HStack
            spacing="$1"
            mt={5}
            mb={-8}
            alignSelf={"center"}
            zIndex={1}
          >
            <For each={props.item.badges}>{(b) => b}</For>
          </HStack>
        }

        <Box class={css.cardHeader}>
          <HStack w="100%">
            {/* Avatar Image */}
            {avatarImage() &&
              <Box class={css.avatarImage} >
                <Image
                  loading="lazy"
                  borderRadius={5}
                  background="$primary1"
                  src={avatarImage()}
                />
              </Box>
            }

            <VStack class={[css.titles].join(" ")}>
              {/* Title */}
              {props.item.title &&
                <Box
                  __tooltip_title={<Box>{props.item.title}</Box>}
                  style={{ "white-space": "nowrap", "overflow": "hidden", "text-overflow": "ellipsis" }}
                  width={"127px !important"} maxH={25} class={css.title} as="h2"
                >
                  {props.item.title}
                </Box>
              }

              {/* Subtitle */}
              {props.item.subtitle &&
                <Box
                  __tooltip_title={<Box>{props.item.subtitle}</Box>}
                  class={css.subtitle}
                >
                  {props.item.subtitle}
                </Box>
              }
            </VStack>
          </HStack>
        </Box>

        <Box class={css.cardFooter}>
        </Box>
      </VStack>
    </Box>
  </>);
};

const hopedVersion = hope(GenericDisplayCard);
export default hopedVersion;