import "@elastic/react-search-ui-views/lib/styles/styles.css";
import { SearchDriver, SearchDriverOptions, SearchResult, SearchState } from "@elastic/search-ui";
import {
  Box,
  Button,
  ButtonGroup,
  Center,
  Checkbox,
  Flex,
  hope,
  HStack,
  Input,
  Select,
  SelectContent,
  SelectIcon,
  SelectListbox,
  SelectOption,
  SelectOptionIndicator,
  SelectOptionText,
  SelectPlaceholder,
  SelectTrigger,
  SelectValue,
  Skeleton,
  Spacer,
  Text
} from "@hope-ui/solid";
import clsx from "clsx";
import { debounce, remove, uniqBy } from "lodash-es";
import Mousetrap from "mousetrap";
import {
  Component,
  createEffect,
  createSignal,
  For,
  JSXElement,
  onCleanup,
  onMount,
  ParentComponent,
  Show
} from "solid-js";
import { createStore } from "solid-js/store";
import { Pagination } from "../common";
import css2 from "./css/search-ui.generic-display.module.sass";
import css from "./css/search-ui.module.scss";
import { SearchUiTypes } from "./search-ui.types";
import { TransitionGroup } from "solid-transition-group";
import MotionFadeIn from "../motion/motion.fade-in";
import { DEFAULT_RESULTS_PER_PAGE } from "~/routes/sheet-music";
import { useSearchParams } from "@solidjs/router";

const SearchBar: Component<{
  placeholderText?: string,
  initialValue?: string,
  onSubmit: (value: string) => void,
  onChange?: (value: string) => void;
}> = (props) => {
  const [value, setValue] = createSignal(props.initialValue ?? "");
  let inputElement!: HTMLInputElement;

  const onEnter = () => {
    props.onSubmit(value());
  };

  const clearInputElement = () => {
    setValue("");
    inputElement.value = "";
  };

  const debouncedOnChange = debounce((value) => {
    props.onChange?.(value);
  }, 250); // A more realistic delay

  onMount(() => {
    let trap = new Mousetrap(inputElement);
    inputElement.value = value();
    trap.bind("enter", onEnter);
    onCleanup(() => trap.unbind("enter"));
  });

  onCleanup(() => {
    debouncedOnChange.cancel();
  });

  return (<>
    <HStack
      zIndex={100}
      padding={5}
      spacing="$3"
    >
      <Box w="100%" class={clsx(["sui-layout-header", css["sui-layout-header"]])}>
        <Box class="sui-layout-header__inner">
          <Box class="sui-search-box">
            <Box class="sui-search-box__wrapper" w="100%">
              <Input
                type="text"
                ref={inputElement}
                placeholder={props.placeholderText ?? "Search..."}
                onKeyUp={(evt) => {
                  let value = evt.currentTarget.value;
                  debouncedOnChange(value);
                  setValue(value);
                }}
              ></Input>
            </Box>
          </Box>
        </Box>
      </Box>
      <ButtonGroup variant="outline" spacing={"$1"}>
        <Button onClick={onEnter}>Search</Button>
        <Button disabled={!value()} onClick={() => {
          clearInputElement();
          onEnter();
        }}>Clear</Button>
      </ButtonGroup>
    </HStack>
  </>);
};

const SearchResultItem: Component<{ result: SearchResult, render: (item: any) => JSXElement; }> = (props) => {
  return (<>
    {props.render(props.result)}
  </>);
};

const SearchPagingInfo: Component<{ searchState: SearchState; }> = (props) => {
  const [start, setStart] = createSignal(0);
  const [end, setEnd] = createSignal(0);

  createEffect(() => {
    const totalResults = props.searchState.totalResults;
    const current = props.searchState.current || 0;
    const resultsPerPage = props.searchState.resultsPerPage || 0;
    const _start = totalResults == 0 ? 0 : (current - 1) * resultsPerPage + 1;
    setStart(_start);
    setEnd(totalResults <= resultsPerPage ? totalResults : _start + resultsPerPage - 1);
  });

  return (<>
    <Box color="$neutral10">
      Showing
      <Text as="strong" color="$neutral11"> {start()} - {Math.min(end(), props.searchState.totalResults)} </Text>
      out of <Text as="strong" color="$neutral11">{props.searchState.totalResults} </Text>

      {props.searchState.searchTerm && <>
          for: <Text as="em" color="$neutral12" fontSize={14}>{props.searchState.searchTerm}</Text>
      </>}

    </Box>
  </>);
};

const SearchResultsPerPage: Component<{
  amountList?: number[],
  value: number,
  onChange: (value: number) => void;
}> = (props) => {
  return (<HStack w={110}>
    <Text color="$neutral11" marginRight={5}>Show</Text>
    <Select
      variant="outline"
      defaultValue={props.value.toString()}
      value={props.value.toString()}
      onChange={(val: string) => {
        props.onChange(parseInt(val));
      }}
    >
      <SelectTrigger>
        <SelectValue/>
        <SelectIcon/>
      </SelectTrigger>
      <SelectContent>
        <SelectListbox>
          <For each={props.amountList || [10, 20, 40, 60]}>
            {item => (
              <SelectOption value={item.toString()}>
                <SelectOptionText>{item}</SelectOptionText>
                <SelectOptionIndicator/>
              </SelectOption>
            )}
          </For>
        </SelectListbox>
      </SelectContent>
    </Select>
  </HStack>);
};

enum SearchSortOptions {
  Newest = "Newest",
  Oldest = "Oldest"
}

const SearchSort: Component<{ value?: SearchSortOptions; onChange: (value: SearchSortOptions) => void; }> = (props) => {
  const [currentAnimation, setCurrentAnimation] = createSignal<SearchSortOptions>(props.value || SearchSortOptions.Newest);

  return (<>
    <Box class="sui-sorting" marginBottom={10}>
      <Box class="sui-sorting__label" color="$neutral12" marginBottom={5}>Sort by</Box>
      <Select
        size={"sm"}
        onChange={(value: string) => {
          let option = SearchSortOptions[value as keyof typeof SearchSortOptions];
          setCurrentAnimation(option);
          props.onChange(option);
        }}
        defaultValue={currentAnimation()}>
        <SelectTrigger opacity={0.8}>
          <SelectPlaceholder>Animation</SelectPlaceholder>
          <SelectValue/>
          <SelectIcon/>
        </SelectTrigger>
        <SelectContent>
          <SelectListbox>
            <For each={Object.values(SearchSortOptions)}>
              {item => (
                <SelectOption value={item}>
                  <SelectOptionText>{item}</SelectOptionText>
                  <SelectOptionIndicator/>
                </SelectOption>
              )}
            </For>
          </SelectListbox>
        </SelectContent>
      </Select>
    </Box>
  </>);
};

const SearchSectionSearchResults: ParentComponent = (prop) => {
  return <Box {...prop} class={css2.cardContainer}>{prop.children}</Box>;
};

const HopeSearchSectionSearchResults = hope(SearchSectionSearchResults);

type SearchResultsProps = {
  results: SearchResult[];
  multiSelectEnabled?: boolean;
  render: (idx: number) => (item: any) => JSXElement;
  onItemContextMenu?: (item: any) => void;
  onSelected?: (items: any[]) => void;
};

const SearchResults: Component<SearchResultsProps> = (props) => {
  const [checkedItems, setCheckedItems] = createStore<boolean[]>(
    (new Array((props.results ?? []).length)).fill(false)
  );
  const allChecked = () => checkedItems.every(Boolean);
  const isIndeterminate = () => checkedItems.some(Boolean) && !allChecked();
  const [itemsSelectedCount, setItemsSelectedCount] = createSignal(0);

  createEffect(() => {
    let items = checkedItems.filter(x => x);
    setItemsSelectedCount(items.length);

    if (props.onSelected) {
      props.onSelected(
        props.results.filter((x, idx) =>
          checkedItems.findIndex((y, idx2) => idx == idx2 && y) > -1
        )
      );
    }
  });

  return (<>
    {props.multiSelectEnabled &&
        <Checkbox
            colorScheme="warning"
            checked={allChecked() && props.results.length > 0}
            indeterminate={isIndeterminate()}
            onChange={(e: any) => setCheckedItems((new Array(props.results.length)).fill(e.target.checked))}
        >
            Select All
            <Box as="span" marginLeft={5} color="$neutral10">
              {itemsSelectedCount() ? `(${itemsSelectedCount()} selected)` : ``}
            </Box>
        </Checkbox>
    }

    <HopeSearchSectionSearchResults>
      <For each={props.results}>
        {(result, idx) => {
          return (<>
            <Show when={props.multiSelectEnabled} fallback={<SearchResultItem result={result}
                                                                              render={props.render(idx())}
            />}>
              <HStack spacing={"$1"}>
                <Checkbox
                  colorScheme="warning"
                  size="lg"
                  checked={checkedItems[idx()]}
                  onChange={(e: any) => setCheckedItems(idx(), e.target.checked)}
                />
                <SearchResultItem result={result} render={props.render(idx())}/>
              </HStack>
            </Show>
          </>);
        }}
      </For>
    </HopeSearchSectionSearchResults>
  </>);
};

type SearchFacetChange = {
  key: string;
  value: string;
  type: "Value" | "Range";
  checked: boolean;
};

type SearchFacetProps = {
  key: string,
  type: string,
  data: { value: string, count: number; }[];
};

const SearchFacet: Component<{
  checkedFacets: { key: string, value: string; }[],
  facet: SearchFacetProps,
  onChange: (output: SearchFacetChange) => void;
}> = (props) => {
  return (<>
    <Box class="sui-multi-checkbox-facet sui-facet">
      {/* Facet Label */}
      <Box
        class="sui-multi-checkbox-facet__label"
        color="$neutral11"
      >
        {props.facet?.key?.toUpperCase()}
      </Box>

      {/* Facet Options */}
      <Box class="sui-multi-checkbox-facet__options-list"
           overflowY={"scroll"} maxHeight={400}
           paddingRight={5}
      >
        <TransitionGroup name="slide-fade">
          <For each={props.facet.data ?? []}>
            {(item) => {
              let isChecked = props.checkedFacets.find(cf => {
                return cf.key.toLocaleLowerCase() == props.facet.key.toLocaleLowerCase()
                  && item.value.toLocaleLowerCase() == cf.value.toLocaleLowerCase();
              }) != null;

              return (<>
                <MotionFadeIn duration={0.5}>
                  <Flex w="100%">
                    <Checkbox
                      checked={isChecked}
                      onMouseDown={() => {
                        props.onChange({
                          key: props.facet.key,
                          value: item.value,
                          type: props.facet.type as "Value" | "Range",
                          checked: !isChecked
                        });
                      }}
                      color="$neutral12"
                      _hover={{ "color": "$accent1" }}
                    >

                      <Box
                        width={"155px !important"}
                        style={{ "white-space": "nowrap", "overflow": "hidden", "text-overflow": "ellipsis" }}
                        _hover={{ "color": "$accent1" }}
                        __tooltip_title={`${item.value}: ${item.count}`}
                        __tooltip_show_arrow={false}
                      >
                        {item.value}
                      </Box>
                    </Checkbox>
                    <Spacer/>
                    <Box
                      as="div"
                      color="$neutral11"
                    >
                      {(item as any).count}
                    </Box>
                  </Flex>
                </MotionFadeIn>
              </>);
            }}
          </For>
        </TransitionGroup>
      </Box>
    </Box>
  </>);
};

type SearchUIViewProps<T> = {
  config: SearchDriverOptions;
  initialSearchTerm?: string;
  onMount?: (driver: SearchDriver) => void;
  onRenderItem: (index: number) => (item: T) => JSXElement;
  onItemContextMenu?: (item: T) => void;
  onDriverStateChange?: (state: SearchState) => void;
  defaultResultsPerPage?: number;
  amountList?: number[];
  multiSelectEnabled?: boolean;
  onMultiSelect?: (items: T[]) => void;
  onFacetLabelKeyMap?: (key: string) => string;
};

const SearchUIView = <T, >(props: SearchUIViewProps<T>) => {
  const driver = new SearchDriver(props.config);
  const [_, setSearchParams] = useSearchParams();
  const [searchState, setSearchState] = createSignal<SearchState>();
  const [resultsPerPage, setResultsPerPage] = createSignal(-1);
  const [defaultSearchSort] = createSignal(SearchSortOptions.Newest);
  const [checkedFacets, setCheckedFacets] = createSignal<{ key: string, value: string; }[]>([]);
  const [resultsLoading, setResultsLoading] = createSignal(true);
  const [multiSelectEnabled] = createSignal(props.multiSelectEnabled);
  let [placeHolderText, setPlaceHolderText] = createSignal("Search...");

  onMount(() => {
    let searchFields = Object.keys(props.config.searchQuery?.search_fields ?? {});
    if (searchFields.length > 0) {
      setPlaceHolderText(`Search by ${searchFields.join(", ")}...`);
    }

    if (props.amountList && props.amountList.length > 0) {
      if (props.defaultResultsPerPage && props.amountList.includes(props.defaultResultsPerPage)) {
        driver.setResultsPerPage(props.defaultResultsPerPage);
      } else {
        driver.setResultsPerPage(props.amountList?.[0] || DEFAULT_RESULTS_PER_PAGE);
      }
    }

    driver.subscribeToStateChanges((state) => {
      setSearchState(state);
      setResultsPerPage(state.resultsPerPage || DEFAULT_RESULTS_PER_PAGE);
      setResultsLoading(state.isLoading);
      if (state.requestId && props.onDriverStateChange) props.onDriverStateChange(state);
    });

    if (props.onMount) props.onMount(driver);
    if (props.initialSearchTerm) driver.getActions().setSearchTerm(props.initialSearchTerm);
    setSort(defaultSearchSort());
  });

  onCleanup(() => {
    driver.reset();
    driver.tearDown();
  });

  const setSort = (value: SearchSortOptions) => {
    driver.getActions().setSort("createdDate", value == SearchSortOptions.Newest ? "desc" : "asc");
  };

  return (<>
    <Box class="sui-layout">
      <Box class={clsx(["sui-layout-body", css["sui-layout-body"]])}>
        <SearchBar
          initialValue={props.initialSearchTerm}
          onSubmit={(value) => {
            if (driver.getState().searchTerm == value) return;
            driver.getActions().setSearchTerm(value, { shouldClearFilters: false });
            setSearchParams({ q: value });
          }}
          onChange={(value) => {
            driver.getActions().setSearchTerm(value, {
              shouldClearFilters: false,
              autocompleteMinimumCharacters: 3,
              debounce: 200,
              refresh: true,
            });
            setSearchParams({ q: value });
          }}
          placeholderText={placeHolderText()}
        />

        <Box class="sui-layout-body__inner" paddingLeft={"15px !important"}>
          <Box class="sui-layout-sidebar" padding="32px 16px 0 0 !important">
            <SearchSort value={defaultSearchSort()} onChange={setSort}/>

            <Box
              className={!searchState()?.filters?.length ? "disabled" : ""}
              cursor={"pointer"}
              color="$neutral11"
              userSelect={"none"}
              _hover={{ "color": "$neutral12" }}
              onclick={() => {
                let filters = searchState()?.filters || [];
                if (filters.length > 0) {
                  setCheckedFacets([]);
                  driver.clearFilters();
                }
              }}
            >Clear {searchState()?.filters?.length} Filter(s)</Box>

            <For each={
              Object
                .values(searchState()?.facets || {})
                .map(x => x as SearchUiTypes.ServerFacets2[])
                .filter(x => x.length > 0)}
            >
              {(facet) => {
                return <SearchFacet
                  checkedFacets={checkedFacets()}
                  facet={{
                    key: facet[0]!.key,
                    type: facet[0]!.type,
                    data: facet.map(x => ({ value: x._id, count: x.count }))
                  }}
                  onChange={(output) => {
                    output.key = props.onFacetLabelKeyMap?.(output.key) ?? output.key;
                    if (output.checked) {
                      driver.addFilter(output.key, output.value.replace(/'/g, "\\'"));
                      setCheckedFacets(f => uniqBy([...f, { key: output.key, value: output.value }], "value"));
                    } else {
                      driver.removeFilter(output.key, output.value);
                      let facets = [...checkedFacets()];
                      remove(facets, (x => x.key == output.key && x.value == output.value));
                      setCheckedFacets(facets);
                    }
                  }}/>;

              }}
            </For>
          </Box>

          <Box class={["sui-layout-main", css["sui-layout-main"]].join(" ")}>
            <Box class="sui-layout-main-header">
              <Box class="sui-layout-main-header__inner">
                {searchState() && <SearchPagingInfo searchState={searchState()!}/>}
                <SearchResultsPerPage amountList={props.amountList} value={resultsPerPage()} onChange={(value) => {
                  driver.setResultsPerPage(value);
                }}/>
              </Box>
            </Box>
            <Center marginBottom={10} w="100%">
              <Pagination
                width={"50%"}
                currentPage={() => searchState()?.current || 1}
                handlePage={(page) => driver.setCurrent(page)}
                totalPages={() => searchState()?.totalPages || 1}
              />
            </Center>
            <Box class="sui-layout-main-body" w="100%">
              <Skeleton w="100%" h="80%"
                        loaded={!resultsLoading()}
                        fadeDuration="0.5s"
                        startColor={"transparent"}
              >
                <SearchResults
                  multiSelectEnabled={multiSelectEnabled()}
                  onSelected={(items) => {
                    props.onMultiSelect?.(items);
                  }}
                  results={searchState()?.results ?? []}
                  render={props.onRenderItem}
                />
              </Skeleton>
            </Box>
            <Center marginBottom={10} w="100%">
              <Pagination
                minWidth={"50%"}
                currentPage={() => searchState()?.current || 1}
                handlePage={(page) => driver.setCurrent(page)}
                totalPages={() => searchState()?.totalPages || 1}
              />
            </Center>
            <Box class="sui-layout-main-footer"></Box>
          </Box>
        </Box>
      </Box>
    </Box>
  </>);
};

export default SearchUIView;