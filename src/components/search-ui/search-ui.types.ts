import {
  APIConnector,
  AutocompleteQueryConfig,
  AutocompleteResponseState,
  QueryConfig,
  RequestState,
  ResponseState,
  SearchResult
} from "@elastic/search-ui";
import { isString } from "lodash-es";
import { capitalizeFirstLetter } from "~/util/helpers";

type PianoRhythmSearchUIResponseState<T> = {
  facets: SearchUiTypes.ServerFacet[];
  meta: {
    page: {
      current: number;
      size: number;
      total_pages: number;
      total_results: number;
    };
    request_id: string;
    alerts: any[];
    warning: any[];
  };
  rawResults: T[];
  results: SearchResult[];
};

export class MyAPIConnector<T> implements APIConnector {
  searchRoute: string = "";
  queryRoute: string = "";

  constructor(apiSearchRoute: string, apiQueryRoute: string = "") {
    this.searchRoute = apiSearchRoute;
    this.queryRoute = apiQueryRoute;
  }

  async onSearch(
    state: RequestState,
    queryConfig: QueryConfig
  ): Promise<ResponseState> {
    // Clean up string values in filters
    state.filters?.forEach(x => {
      x.values = x.values.map((value) => {
        if (isString(value)) {
          return value.replace(/\\'/g, "'");
        }
        return value;
      });
    });

    let payload: SearchUiTypes.ServerSearchUIPayload = {
      request: { ...state },
      query: SearchUiTypes.QueryConfigRequest.FromConfig(queryConfig),
      usertag: (queryConfig as any)?.usertag
    };

    const response = await fetch(this.searchRoute, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    });

    const responseJSON = await response.json();
    let data = responseJSON as PianoRhythmSearchUIResponseState<T>;

    // response will need to be in the shape of ResponseState.
    // Alternatively you could transform the response here
    return {
      facets: data.facets as any,
      totalPages: data.meta.page.total_pages,
      totalResults: data.meta.page.total_results,
      requestId: data.meta.request_id,
      wasSearched: true,
      resultSearchTerm: state.searchTerm,
      results: data.results,
      rawResponse: response
    } as ResponseState;
  }

  async onAutocomplete(
    state: RequestState,
    _queryConfig: AutocompleteQueryConfig
  ): Promise<AutocompleteResponseState> {
    return {
      autocompletedResults: [],
      autocompletedResultsRequestId: "",
      autocompletedSuggestionsRequestId: "",
      autocompletedSuggestions: {}
    };
  }

  onResultClick(_params: any): void {
    console.log(
      "perform a call to the API to highlight a result has been clicked"
    );
  }

  onAutocompleteResultClick(_params: any): void {
    console.log(
      "perform a call to the API to highlight an autocomplete result has been clicked"
    );
  }
}

export namespace SearchUiTypes {
  export type ServerFacetType =
    | "Value"
    | "Range"
    | ["Bool", boolean]
    | "Unknown";

  export type ServerFacet = {
    type: ServerFacetType;
    ranges?: any[];
    size?: number;
  };

  export type ServerFacets2 = {
    _id: string;
    count: number;
    key: string;
    type: string;
  };

  export type ServerFacets = {
    _id: string;
    facet: ServerFacet;
    data?: [string, [{ count: number, value: string; }[]]];
  };

  export type ServerFacetResponse = {
    key: string;
    type: "Value" | "Range";
    data?: [string, [{ count: number, value: string; }[]]];
  };

  export type QueryConfigRequest = {
    facets: ServerFacets[];
    search_fields: string[];
    usertag?: string;
  };

  export namespace QueryConfigRequest {
    export const FromConfig = (config: QueryConfig): QueryConfigRequest => {
      return {
        search_fields: config.search_fields ? Object.keys(config.search_fields as any) : [],
        // @ts-ignore
        facets: config.facets ?
          Object
            .entries(config.facets)
            .map(([key, facet]) => {
              facet.type = capitalizeFirstLetter(facet.type);
              return { key, facet: facet as ServerFacet };
            })
          : []
      };
    };
  }

  export type ServerSearchUIPayload = {
    request: RequestState;
    query: QueryConfigRequest;
    usertag?: string;
  };
}