$gridGap: 2rem
$imgSize: 50px

.cardContainer
  width: 100%
  max-width: 1200px
  margin: 0 auto
  position: relative
  padding: 1rem 0.5rem
  margin-bottom: 20px
  display: grid
  align-items: start
  justify-items: center
  grid-gap: 2rem $gridGap
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr))

  @media screen and (min-width: 1101px)
    grid-template-columns: repeat(auto-fill, minmax(25%, 1fr))

.card
  display: flex
  flex-direction: column
  align-items: flex-start
  position: relative
  width: 100%
  height: 100%
  color: var(--hope-colors-neutral12)
  z-index: 1
  padding: 5px
  cursor: pointer
  filter: drop-shadow(2px 4px 4px var(--hope-colors-primaryDark1))
  background: var(--hope-colors-primaryDark2)
  --displayFooter: 0

  &:hover
    --displayFooter: 1
    --displaySubcategory: 1
    --subCategoryColor: var(--hope-colors-primaryDark1)

    &::after
      background: var(--hope-colors-primaryDark1)

  &::after
    cursor: inherit
    background: var(--hope-colors-primaryDark2)
    content: ''
    position: absolute
    top: 0
    right: 0
    bottom: 0px
    margin: -5px
    left: 0
    border-radius: 10px
    overflow: hidden
    z-index: -1
    transition: 100ms ease all

.cardHeader
  margin-top: 5px
  cursor: inherit
  display: flex
  align-items: center
  order: 3
  width: 100%

.category
  background: var(--hope-colors-primaryDarkAlpha2)
  text-transform: uppercase
  font-size: 14px
  display: flex
  align-items: center
  justify-content: center
  opacity: 0.5
  z-index: 1
  position: relative

.title
  color: var(--hope-colors-tertiary1)
  font-weight: 600
  font-size: 16px !important
  margin: 0 0 0.25rem 0.25rem
  display: block

.subtitle
  cursor: inherit
  color: var(--hope-colors-neutral10)
  font-size: 14px
  line-height: 1.2
  display: flex
  align-items: center
  margin-left: 0.25rem

.titles, .title
  cursor: inherit
  width: 100%
  overflow: hidden

.titles
  align-items: flex-start
  flex: 1

.cardThumbnailContainer
  height: 150px
  overflow: hidden
  width: 100%
  border: 2px solid var(--hope-colors-primaryLight)
  border-radius: 6px
  position: relative

.thumbnail
  position: relative
  transition: 400ms all ease
  width: 100%
  height: 100%
  overflow: hidden

  img
    object-fit: contain
    width: inherit
    height: inherit

  @if $gridGap == 3rem
    margin: -3px auto
    display: block

.avatarImage
  display: block
  position: relative
  z-index: 1
  margin-top: 7px

  img
    border-radius: 6px
    display: block
    width: $imgSize
    height: $imgSize
    padding: 2px
    background: var(--hope-colors-primaryLight)
    overflow: hidden
    font: 10px/1 monospace
