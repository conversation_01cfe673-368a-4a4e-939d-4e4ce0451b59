import { Box, Button, Center, createDisclosure, Divider, Flex, Heading, Icon, List, Modal, ModalBody, ModalCloseButton, ModalContent, ModalOverlay, Skeleton, useColorMode, VStack } from "@hope-ui/solid";
import { BiSolidUserCircle } from 'solid-icons/bi';
import { FaBrandsDiscord, FaBrandsGithub, FaSolidFileAudio, FaSolidGlobe, FaSolidHeadphones, FaSolidHeadphonesSimple, FaSolidKeyboard, FaSolidLanguage, FaSolidMusic, FaSolidNoteSticky, FaSolidSliders, FaSolidToolbox, FaSolidXmark } from 'solid-icons/fa';
import { ImCogs } from 'solid-icons/im';
import { VsSignOut } from "solid-icons/vs";
import { catchError, Component, createEffect, createSignal, ErrorBoundary, For, JSXElement, lazy, onMount, Show, Suspense } from "solid-js";
import { useService } from "solid-services";
import tinycolor from "tinycolor2";
import { SettingsSubTextGroups } from "~/components/SettingsMenu";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import I18nService from "~/services/i18n.service";
import WebsocketService from "~/services/websocket.service";
import { COMMON } from "~/util/const.common";
import { blurActiveElement, getRootUITheme } from "~/util/helpers.dom";
import { logError } from "~/util/logger";
import { MenuContentDivider } from "./settings-content/common.content";
import ApplicationContent from "./settings-content/general-application.content";
import LanguageContent from "./settings-content/general-language.content";
import AppSettingsService from "~/services/settings-storage.service";
import AppThemesService from "~/services/app.themes.service";
import LoginService from "~/services/login.service";

const DiscordContent = lazy(() => import("./settings-content/discord.content"));
const GraphicsGeneralContent = lazy(() => import("./settings-content/graphics-general.content"));
const GraphicsUIContent = lazy(() => import("./settings-content/graphics-ui.content"));
const GraphicsWorldContent = lazy(() => import("./settings-content/graphics-world.content"));
const InputContent = lazy(() => import("./settings-content/online-input.content"));
const MyAccountContent = lazy(() => import("./settings-content/online-myaccount.content"));
const AudioSoundBGMusicContent = lazy(() => import("./settings-content/audio-bg-music.content"));
const AudioMidiContent = lazy(() => import("./settings-content/audio-midi.content"));
const AudioSoundEffectsContent = lazy(() => import("./settings-content/audio-sound-effects.content"));
const AudioSoundfontContent = lazy(() => import("./settings-content/audio-soundfont.content"));
// const SubscriptionContent = lazy(() => import("./settings-content/billing-subscription.content"));
const GitHubIssuesView = lazy(() => import("~/components/github-issues-view"));

type SettingsHeaderGroups =
  "General"
  | "Online"
  | "Graphics"
  | "Audio"
  | "Billing"
  | "Other";

type SettingsTextLabels =
  | "Language"
  | "Application"
  | "My Account"
  | "Multiplayer"
  | "Discord"
  | "Input"
  | "General"
  | "UI"
  | "World"
  | "Soundfont"
  | "Midi";

type ServiceProps = {
  websocketService?: () => ReturnType<typeof WebsocketService>,
  displayService?: () => ReturnType<typeof DisplaysService>,
  appService: () => ReturnType<typeof AppService>,
  loginService?: () => ReturnType<typeof LoginService>,
};

const TextGroups = (props: ServiceProps): SettingsSubTextGroups<any, any>[] => {
  let output = [
    {
      header: "General", texts: [
        { label: "Language", icon: () => <Icon as={FaSolidLanguage} />, content: () => LanguageContent },
        { label: "Application", icon: () => <Icon as={FaSolidToolbox} />, content: () => ApplicationContent },
        // TODO: Disabled for now
        // { label: "Plugins", skipTranslate: true, icon: () => <Icon as={FaSolidPlug} />, content: () => PluginsContent },
      ]
    },
    {
      header: "Online", texts: [
        { label: "My Account", icon: () => <Icon as={BiSolidUserCircle} />, content: () => MyAccountContent },
        { label: "Discord", icon: () => <Icon as={FaBrandsDiscord} />, content: () => DiscordContent },
        { label: "Input", icon: () => <Icon as={FaSolidKeyboard} />, content: () => InputContent },
      ]
    },
    {
      header: "Graphics", texts: [
        { label: "General", icon: () => <Icon as={ImCogs} />, content: () => GraphicsGeneralContent },
        { label: "World", icon: () => <Icon as={FaSolidGlobe} />, content: () => GraphicsWorldContent },
        { label: "UI", icon: () => <Icon as={FaSolidSliders} />, content: () => GraphicsUIContent },
      ]
    },
    {
      header: "Audio", texts: [
        { label: "Soundfont", icon: () => <Icon as={FaSolidHeadphones} />, content: () => AudioSoundfontContent },
        { label: "MIDI", skipTranslate: true, icon: () => <Icon as={FaSolidFileAudio} />, content: () => AudioMidiContent },
        { label: "Effects", icon: () => <Icon as={FaSolidHeadphonesSimple} />, content: () => AudioSoundEffectsContent },
        { label: "BG Music", skipTranslate: true, icon: () => <Icon as={FaSolidMusic} />, content: () => AudioSoundBGMusicContent },
      ]
    },
    // {
    //   header: "Billing", texts: [
    //     { label: "Subscription", icon: () => <Icon as={FaSolidCreditCard} />, content: () => SubscriptionContent },
    //   ]
    // },
    {
      addDivider: true,
      texts: [
        {
          header: "Other",
          label: "Issues Tracker",
          isNew: true,
          icon: () => <Icon as={FaBrandsGithub} />,
          content: () => <ErrorBoundary fallback={<Box>Failed to load content...</Box>}><GitHubIssuesView /></ErrorBoundary>,
        },
        {
          header: "Other",
          label: "Changelog",
          // skipTranslate: true,
          icon: () => <Icon as={FaSolidNoteSticky} />,
          onClick: (input: any) => {
            input.resetSettingToDefault();
            props.displayService?.().setDisplay("RELEASE_NOTES", true);
          }
        },
        {
          header: "Other",
          label: "Credits",
          icon: () => <Icon as={FaSolidNoteSticky} />,
          onClick: (input: any) => {
            input.resetSettingToDefault();
            props.displayService?.().setDisplay("CREDITS", true);
          }
        },
        {
          label: "Log out",
          color: 'red',
          icon: () => <Icon as={VsSignOut} />,
          skipTranslate: true,
          onClick: (input: any) => {
            input.resetSettingToDefault();
            props.loginService?.().tryLogout();
          }
        },
      ]
    }
  ];

  // @ts-ignore
  return output.map(o => {
    let texts = o.texts.filter(Boolean);
    return { ...o, texts };
  });
};

type ActiveSetting = { group: SettingsHeaderGroups, label: SettingsTextLabels; };

const SettingsModal: Component = () => {
  const DISPLAY_KEY = "SETTINGS_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();

  const appService = useService(AppService);
  const appThemesService = useService(AppThemesService);
  const appSettingsService = useService(AppSettingsService);
  const displayService = useService(DisplaysService);
  const i18nService = useService(I18nService);
  const websocketService = useService(WebsocketService);
  const loginService = useService(LoginService);

  const [activeSetting, setActiveSetting] = createSignal<ActiveSetting>({ group: "General", label: "Language" });
  const [activeContent, setActiveContent] = createSignal<JSXElement>();
  const [resetActiveSetting, setResetActiveSetting] = createSignal<() => void>();
  const [hasContent, setHasContent] = createSignal<boolean>(false);
  const [textGroups] = createSignal(TextGroups({ appService, websocketService, displayService, loginService }));
  const { colorMode } = useColorMode();
  const [menuBackgroundColor, setMenuBackgroundColor] = createSignal<string>("transparent");
  const [menuTextColor, setMenuTextColor] = createSignal<string>("$neutral12");

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  onMount(() => {
    blurActiveElement();

    try {
      let firstElement = textGroups()?.[0];
      let firstHeader = firstElement?.header;
      let firstElementText = firstElement?.texts?.[0];
      let firstLabel = firstElementText?.label;
      let firstContent = firstElementText?.content;

      if (firstHeader && firstLabel) {
        setActiveSetting({ group: firstHeader, label: firstLabel });
        setHasContent(firstContent != null);

        if (firstContent) {
          setActiveContent(firstContent);
          setResetActiveSetting(() => {
            return () => {
              setActiveSetting({ group: firstHeader, label: firstLabel });
              setHasContent(firstContent != null);
              setActiveContent(firstContent);
            };
          });
        }
      }
    } catch { }
  });

  createEffect(() => {
    if (displayService().getDisplay(DISPLAY_KEY)) { onOpen(); } else { onClose(); }
  });

  createEffect(() => {
    //Note: this is just to capture any theme changes
    appThemesService().themeColors.accent;
    setMenuBackgroundColor(`${colorMode() == "dark" ? "$accent10" : "$accent1"}`);
  });

  createEffect(() => {
    //Note: this is just to capture any theme changes
    appThemesService().themeColors.accent;
    let root = getRootUITheme();
    let bgColor = root?.style.getPropertyValue(`--hope-colors-${menuBackgroundColor().replace("$", "")}`);
    let tinyBgColor = tinycolor(bgColor);
    setMenuTextColor(tinyBgColor.isLight() ? "$neutral12" : "$neutral3");
  });

  catchError(() => { }, (err) => {
    logError(`[Settings View] Load Content Error ${JSON.stringify(err.message)}`);
    if (appSettingsService().isDebugMode()) console.error(err);
  });

  return (<>
    <Modal
      opened={isOpen()} onClose={closeModal}
      scrollBehavior={"inside"}
      size="full"
      preserveScrollBarGap={false}
    >
      <ModalOverlay background={"$primary1"} />
      <ModalContent overflow={"hidden"}>
        <ModalBody
          position="relative"
          overflow="hidden"
        >
          <Center>
            <Flex width={{
              "@initial": "100%",
              "@lg": "70%"
            }}
              height={"calc(100vh - 20px)"}
              margin={0}
              paddingBottom={20}
            >
              {/* Left Menu */}
              <Box
                width={"210px"}
                paddingTop={10}
                overflowX="hidden"
                overflowY={"scroll"}
              >
                <List spacing="$2">
                  <For each={textGroups()}>{(input) =>
                    <>
                      {input.addDivider && <MenuContentDivider marginBoth={0} />}

                      {/* Menu Headers */}
                      {input.header && <Heading
                        textTransform="uppercase"
                        color={"$neutral11"}
                        fontSize={"10px !important"}
                      >
                        {i18nService().t_roomPageSettingHeader((input.header as string).toLowerCase())}
                      </Heading>
                      }

                      {/* Menu Buttons */}
                      <List spacing="$2">
                        <For each={input.texts}>{(subText) =>
                          <Button
                            leftIcon={subText.icon}
                            _hover={{ "background": `${colorMode() == "dark" ? "$accent1" : "$accent5"} !important` }}
                            background={
                              activeSetting().group == input.header &&
                                activeSetting().label == subText.label
                                ? menuBackgroundColor() : "transparent"
                            }
                            onMouseDown={() => {
                              setActiveSetting({ group: input.header, label: subText.label });
                              setHasContent(subText.content != null);
                              if (subText.content) setActiveContent(subText.content);
                              if (subText.onClick) subText.onClick({
                                resetSettingToDefault: () => resetActiveSetting()!()
                              });
                            }}
                            width={"100%"}
                            display="flex"
                            justifyContent={"flex-start"}
                            padding={0}
                            paddingLeft={5}
                            height={30}
                            position={"relative"}
                            ref={(el: HTMLButtonElement) => buttonSFX(el)}
                            color={activeSetting()?.group == input.header &&
                              activeSetting()?.label == subText.label ? menuTextColor() : "$neutral12"}
                          >
                            {subText.isNew &&
                              <Box
                                position={"absolute"}
                                top={0}
                                right={0}
                                background={"$accent2"}
                                padding={2}
                                borderRadius={5}
                                fontSize={"$xs"}
                              >New</Box>
                            }
                            {
                              !subText.skipTranslate && input.header && subText.label ?
                                i18nService().t_roomPageSettingSubMenuLabel(
                                  (input.header as string).toLowerCase(),
                                  (subText.label as string).toLowerCase()
                                ) : subText.label
                            }
                          </Button>
                        }
                        </For>
                      </List>
                    </>
                  }
                  </For>
                  <Box left={"110%"} top={0} height={"100%"} position="absolute">
                    <Divider orientation="vertical" color={"gray"} opacity={0.5} />
                  </Box>
                </List>
              </Box>

              {/* Divider */}
              <Box left={"0%"} top={0} height={"100%"}>
                <Divider orientation="vertical" color={"gray"} opacity={0.5} />
              </Box>

              {/* Body Content */}
              <Box
                paddingRight={10}
                marginTop={30}
                position="relative"
                marginLeft={28}
                width="100%"
                height="100%"
                overflowY={"auto"}
                overflowX="hidden"
              >
                <Show when={hasContent()} fallback={<Box>No content available, yet.</Box>}>
                  <VStack
                    w="100%"
                    justifyContent={"flex-start"}
                    alignItems={"flex-start"}
                    spacing={"$8"}
                  >
                    <Suspense fallback={<Skeleton w="100vw" h="100vh" />}>
                      <ErrorBoundary fallback={<Box>Failed to load content...</Box>}>
                        {activeContent()}
                      </ErrorBoundary>
                    </Suspense>
                  </VStack>
                </Show>
              </Box>
            </Flex>
          </Center>
        </ModalBody>

        <ModalCloseButton
          __tooltip_title={"Close Settings"}
          __tooltip_placement="left"
          marginRight={{
            "@initial": 23,
            "@lg": 0
          }}
          marginTop={{
            "@initial": -10,
            "@lg": 0
          }}
          icon={<FaSolidXmark />}
          color="$neutral12"
          h={25}
          border={"1px solid gray"}
          _hover={{ "background": "$accent1" }}
        />
      </ModalContent>
    </Modal>
  </>);
};

export default SettingsModal;