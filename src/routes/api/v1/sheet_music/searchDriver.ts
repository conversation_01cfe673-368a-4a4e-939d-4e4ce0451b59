import { z } from "zod";
import type { APIEvent } from "@solidjs/start/server";
import { SearchDriverAggregate } from "../../search-driver.helper";
import { mapSheetMusicDboToDto, SheetMusicDboZ, SheetMusicDtoZ } from "~/models/sheet-music-dbo.models";
import { SheetMusicDBService } from "~/lib/db/services/db-sheet-music";
import { UserDBService } from "~/lib/db/services/db-user";

export const POST = (event: APIEvent) =>
  SearchDriverAggregate<z.infer<typeof SheetMusicDboZ>, z.infer<typeof SheetMusicDtoZ>>(event, {
    collection: SheetMusicDBService.collectionName,
    outputDboType: SheetMusicDboZ,
    outputDtoType: SheetMusicDtoZ,
    usersService: UserDBService.getInstance(),
    databaseService: SheetMusicDBService.getInstance(),
    additionalFilters: (hasElevatedAccess: boolean, searchTerm) => {
      return hasElevatedAccess ? [] : [
        // Users without access can only see approved and public/unlisted sheet music
        { "field": "approved", values: [true] },
        { "field": "meta.privacyStatus", values: ["Public", searchTerm ? "Unlisted" : ""].filter(Boolean) }
      ];
    },
    fieldsThatAreBasicUnions: (key: string) => {
      switch (key.toLowerCase()) {
        case "meta.privacystatus":
        case "difficultylevel":
        case "category":
          return true;
        default:
          return false;
      }
    },
    mapFacetName: (key: string) => {
      switch (key.toLowerCase()) {
        case "approved":
          return "meta.approved";
        case "views":
          return "meta.views";
        case "difficulty":
          return "difficultyLevel";
        case "my favorites":
          return "favorites";
        default:
          return key;
      }
    },
    onMapGetFacetResults: (facetsResults, usertag) => {
      let favorites = facetsResults["favorites"];
      if (favorites) {
        favorites = favorites.filter((favorite) => favorite._id == usertag);
        favorites.forEach((favorite) => {
          favorite.key = "My Favorites";
        });
        facetsResults["favorites"] = favorites;
      }

      let approved = facetsResults["approved"];
      if (approved) {
        approved.forEach((approved) => {
          approved.key = "Approved";
        });
        facetsResults["approved"] = approved;
      }

      let difficultyLevel = facetsResults["difficulty"];
      if (difficultyLevel) {
        difficultyLevel.forEach((difficultyLevel) => {
          difficultyLevel.key = "Difficulty";
        });
        facetsResults["difficulty"] = difficultyLevel;
      }

      return facetsResults;
    },
    onMapDboToDto: function (dbos) {
      return dbos.map(mapSheetMusicDboToDto);
    }
  });