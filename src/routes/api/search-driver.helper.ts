import type { APIEvent } from "@solidjs/start/server";
import { z } from "zod";
import { IBaseDBService } from "~/lib/db/db-store";
import { FacetsZ, FilterZ, SearchDriverInputZ } from "~/models/search-driver.models";
import { UserDbo } from "~/models/user-dbo.models";
import { UserDBService } from "~/lib/db/services/db-user";

type SearchDriverInput = z.infer<typeof SearchDriverInputZ>;

type FacetResult = {
  _id: string;
  count: number;
  key: string;
  type: string;
};

type EndpointBaseSearchDriverAggregateProps<DBO, DTO> = {
  collection: string;
  databaseService: IBaseDBService<DBO>;
  usersService: UserDBService;
  onSort?: (input: SearchDriverInput) => object;
  mapFacetName?: (input: string) => string;
  fieldsThatAreBasicUnions?: (input: string) => boolean;
  fieldsThatRequireElevatedAccess?: (input: string) => boolean;
  additionalFilters?: (hasElevatedAccess: boolean, searchTerm?: string, usertag?: string) => {
    field: string;
    values: (string | boolean | number)[];
  }[];
  onMapFilters?: (filters: z.infer<typeof FilterZ>[]) => z.infer<typeof FilterZ>[];
  onMapFacets?: (filters: z.infer<typeof FacetsZ>[]) => z.infer<typeof FacetsZ>[];
  onMapGetFacetResults?: (input: Record<string, FacetResult[]>, usertag?: string) => Record<string, FacetResult[]>;
  onValidateElevatedAccess?: (cookie: string) => Promise<boolean>;
  outputDboType: z.ZodTypeAny;
  outputDtoType: z.ZodTypeAny;
  onMapDboToDto: (input: DBO[]) => DTO[];
};

export const SearchDriverAggregate = async <DBO, DTO>({ request }: APIEvent, props: EndpointBaseSearchDriverAggregateProps<DBO, DTO>) => {
  try {
    const requestInput = await request.json();
    const driverInputParse = await SearchDriverInputZ.safeParseAsync(requestInput);

    if (driverInputParse.error) {
      return new Response(driverInputParse.error.toString(), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const driverInput = driverInputParse.data;
    let hasElevatedAccess = false;
    let usertag: string | undefined = driverInput?.usertag;

    // Check if user has elevated access
    if (usertag) {
      hasElevatedAccess = await props.usersService.userHasSheetMusicEditAccess(usertag);
    }

    let mappedFilters = props.onMapFilters?.(driverInput.request.filters as any);
    if (mappedFilters && mappedFilters.length > 0) driverInput.request.filters = mappedFilters;

    let mappedFacets = props.onMapFacets?.(driverInput.query.facets as any);
    if (mappedFacets && mappedFacets.length > 0) driverInput.query.facets = mappedFacets;

    let additionalFilters = props.additionalFilters?.(hasElevatedAccess, driverInput.request.searchTerm, usertag) ?? [];
    if (additionalFilters.length > 0) {
      driverInput.request.filters.push(...additionalFilters.map(x => ({
        field: x.field,
        values: x.values,
        type: "any"
      }) as any));
    }

    // Check for facets that require elevated access
    if (driverInput.query.facets.some((facet) => props.fieldsThatRequireElevatedAccess?.(facet.key))) {
      if (!hasElevatedAccess) {
        // Remove facets that require elevated access
        driverInput.query.facets = driverInput.query.facets.filter((facet) => !props.fieldsThatRequireElevatedAccess?.(facet.key));
      }
    }

    // Create facets pipeline
    const facetsPipeline = driverInput.query.facets.reduce<Record<string, any[]>>((acc, facet) => {
      let _fieldName = `${props.mapFacetName?.(facet.key) || facet.key}`;
      let valueFieldName = `$${_fieldName}`;

      switch (facet.facet.type.toLowerCase()) {
        case "range": {
          let facetRanges = facet.facet.ranges || [];
          let ranges = Array.from(new Set(facetRanges.flatMap(x => [x.from, x.to || 0]).sort()));

          acc[facet.key] = [
            { "$unwind": valueFieldName },
            {
              "$bucket": {
                "groupBy": valueFieldName,
                "boundaries": ranges,
                "default": "default",
                "output": { "count": { "$sum": 1 } }
              }
            },
            {
              "$project": {
                "_id": { "$toString": "$_id" },
                "count": "$count",
                "lowerBound": "$_id",
                "upperBound": {
                  "$arrayElemAt": [
                    ranges,
                    {
                      "$add": [
                        { "$indexOfArray": [ranges, "$_id"] },
                        1
                      ]
                    }
                  ]
                }
              }
            },
            {
              "$project": {
                "_id": {
                  "$concat": [
                    { "$toString": "$lowerBound" },
                    " - ",
                    { "$toString": "$upperBound" }
                  ]
                },
                "count": "$count",
                "key": _fieldName,
                "type": facet.facet.type
              }
            },
            // Remove the default bucket
            {
              $match: {
                _id: {
                  $not: {
                    $regex: 'default', $options: 'i'
                  }
                }
              }
            }

          ];
          break;
        }
        case "value": {
          acc[facet.key] = [
            { $unwind: valueFieldName },
            { $sortByCount: valueFieldName },
            { $sort: { _id: 1 } },
            {
              $project: {
                _id: { $toString: { $ifNull: ["$_id.Case", "$_id"] } },
                count: "$count",
                key: _fieldName,
                type: facet.facet.type
              }
            }
          ];
          break;
        }
        case "bool": {
          acc[facet.key] = [
            {
              $group: {
                _id: valueFieldName,
                count: { $sum: 1 }
              }
            },
            {
              $project: {
                _id: { $toString: { $ifNull: ["$_id.Case", "$_id"] } },
                type: facet.facet.type,
                key: _fieldName,
                count: 1
              }
            }
          ];
          break;
        }
      }
      return acc;
    }, {});

    // Create filter pipeline
    let filterMatch = { $match: {} as Record<string, any> };
    driverInput.request.filters.filter(Boolean).forEach((filter) => {
      filter.values = filter.values.filter(Boolean);
      if (filter.values.length == 0) return;

      // let fieldName = `${filter.field}`;
      let fieldName = `${props.mapFacetName?.(filter.field) ?? filter.field}`;

      if (props.fieldsThatAreBasicUnions?.(fieldName)) {
        fieldName += `.Case`;
      }

      // Clean up the filter values by unescaping them
      filter.values = filter.values.map((value) => {
        if (value.replace) return value.replace(/\\'/g, "'");
        return value;
      });

      // Convert filter values if they are booleans
      let values = filter.values.map((value) => {
        if (value.toLowerCase && value.toLowerCase() === "true") return true;
        if (value.toLowerCase && value.toLowerCase() === "false") return false;
        return value;
      });

      filterMatch.$match[fieldName] = { $in: values };
    });

    // Create search term pipeline
    let searchTermMatch = { $match: {} as { $or?: Array<Record<string, any>>; } };
    if (driverInput.request.searchTerm) {
      let searchTerm = driverInput.request.searchTerm;

      // Search for documents using the search term by matchin using fields from the input's query search fields
      let searchFields = driverInput.query.search_fields.map((field) => {
        let fieldName = `${field}`;
        if (props.fieldsThatAreBasicUnions?.(field)) {
          fieldName += `.Case`;
        }
        return fieldName;
      });

      // Using the search fields, search by using an 'or' operator in a $match stage
      searchTermMatch.$match["$or"] = searchFields.map((field) => {
        return { [field]: { $regex: searchTerm, $options: "i" } };
      });
    }

    let getFacetsResult = await props.databaseService.aggregateData([
      searchTermMatch,
      filterMatch,
      { $facet: facetsPipeline }
    ]);

    let source: Record<string, any>[] = [{ $match: {} }];
    source.push(searchTermMatch);
    source.push(filterMatch);

    // Handle sorting
    let sortDirection = driverInput.request.sortDirection?.toLowerCase();
    let sortField = driverInput.request.sortField;

    if (sortDirection && sortField) {
      let isAsc = sortDirection === "asc" || sortDirection === "ascending" || sortDirection == "oldest";
      let sort = { $sort: {} as { [key: string]: number; } };
      sort.$sort[sortField] = isAsc ? 1 : -1;
      source.push(sort);
    }

    let countPipeline = await props.databaseService.aggregateData<{ total: number; }>([...source, { $count: "total" }]);
    let responseTotal = countPipeline[0]?.total ?? 0;

    let current = Math.max((driverInput.request.current - 1), 0);
    source.push({ $skip: driverInput.request.resultsPerPage * current });
    source.push({ $limit: driverInput.request.resultsPerPage });

    let output = await props.databaseService.aggregateData(source);

    let totalPages = responseTotal ? Math.ceil(responseTotal / (driverInput.request.resultsPerPage || 1)) : 0;
    let facetResults = getFacetsResult[0] as any as Record<string, FacetResult[]>;
    if (props.onMapGetFacetResults) facetResults = props.onMapGetFacetResults(facetResults, usertag);

    return new Response(JSON.stringify({
      facets: facetResults,
      meta: {
        page: {
          current: driverInput.request.current,
          size: output.length,
          total_pages: totalPages,
          total_results: responseTotal
        },
        request_id: "", alerts: [], warning: []
      },
      results: props.onMapDboToDto?.(output)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error(error);
    return new Response(error ? `${error}` : "An error occurred", { status: 400 });
  }
};