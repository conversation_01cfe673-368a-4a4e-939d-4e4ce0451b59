# Sheet Music Route - Refactored Structure

This directory contains the refactored sheet music route with improved organization, separation of concerns, and maintainability.

## Structure

```
src/routes/sheet-music/
├── [[id]].tsx                    # Main route component (simplified)
├── README.md                     # This documentation
├── index.ts                      # Barrel exports
├── components/                   # UI components
│   ├── sheet-music-navigation-bar.tsx
│   ├── sheet-music-search-view.tsx
│   ├── sheet-music-error-boundary.tsx
│   └── sheet-music-loading.tsx
├── hooks/                        # Custom hooks
│   ├── use-sheet-music-initialization.tsx
│   ├── use-sheet-music-search.tsx
│   └── use-user-authentication.tsx
└── config/                       # Configuration files
    └── search-config.ts
```

## Key Improvements

### 1. Separation of Concerns
- **Main Component**: Only handles UI composition and local state
- **Custom Hooks**: Handle specific business logic (initialization, search, auth)
- **Components**: Reusable UI components with clear responsibilities
- **Configuration**: Centralized configuration management

### 2. Custom Hooks

#### `useSheetMusicInitialization`
- Handles audio system initialization
- Manages service lifecycle (mount/cleanup)
- Encapsulates complex async initialization logic

#### `useSheetMusicSearch`
- Manages search configuration based on user permissions
- Handles local storage for user preferences
- Provides memoized search configuration

#### `useUserAuthentication`
- Handles user session management
- Manages authentication state
- Provides user permission checks

### 3. Component Extraction

#### `SheetMusicNavigationBar`
- Extracted navigation logic
- Handles logo loading and navigation
- Manages upload button visibility

#### `SheetMusicSearchView`
- Encapsulates search UI logic
- Handles card rendering and interactions
- Manages search driver integration

#### `SheetMusicErrorBoundary`
- Provides error handling for the entire page
- Shows user-friendly error messages
- Includes retry functionality

### 4. Configuration Management
- Centralized search configuration
- Type-safe constants
- Easy to modify and maintain

## Benefits

1. **Maintainability**: Each piece has a single responsibility
2. **Testability**: Hooks and components can be tested in isolation
3. **Reusability**: Components and hooks can be reused elsewhere
4. **Readability**: Main component is much cleaner and easier to understand
5. **Error Handling**: Better error boundaries and user experience
6. **Performance**: Memoized configurations and optimized re-renders

## Usage

The main component now focuses on composition:

```tsx
export default function MainPage() {
  // Custom hooks handle complex logic
  useSheetMusicInitialization();
  const { user } = useUserAuthentication();
  const { driverOptions, defaultResultsPerPage, saveResultsPerPage, RESULTS_PER_PAGE } = useSheetMusicSearch();

  // Simple local state
  const [selectedSheets, setSelectedSheets] = createSignal<SheetMusicDto[]>([]);
  const [activeDetailID, setActiveDetailID] = createSignal<string>();

  // Clean UI composition
  return (
    <SheetMusicErrorBoundary>
      <Box>
        <SheetMusicNavigationBar />
        <SheetMusicSearchView {...props} />
      </Box>
    </SheetMusicErrorBoundary>
  );
}
```

## Migration Notes

- All functionality is preserved
- No breaking changes to external APIs
- Improved error handling and user experience
- Better TypeScript support and type safety
- Removed dead code and unused imports

## Future Improvements

1. Add loading states to hooks
2. Implement proper caching strategies
3. Add more comprehensive error handling
4. Consider adding analytics hooks
5. Add unit tests for all hooks and components
