import { Badge } from "@hope-ui/solid";
import { lazy, Suspense } from "solid-js";
import { useService } from "solid-services";
import MotionFadeIn from "~/components/motion/motion.fade-in";
import { GenericDefaultBadges } from "~/components/search-ui/generic-repo";
import GenericDisplayCard, { GenericDisplayCardItem } from "~/components/search-ui/search-ui.generic-display";
import { SheetMusicDto, SheetMusicDtoHelpers } from "~/models/sheet-music-dbo.models";
import AppService from "~/services/app.service";
import { SheetMusicService } from "~/services/sheet-music.service";
import { IMAGES } from "~/util/const.common";

const SearchUIView = lazy(() => import("~/components/search-ui/search-ui.base"));

interface SheetMusicSearchViewProps {
  config: any;
  initialSearchTerm?: string;
  defaultResultsPerPage: number;
  resultsPerPageOptions: number[];
  onSelectedSheetsChange: (sheets: SheetMusicDto[]) => void;
  onActiveDetailChange: (id: string) => void;
  onDriverStateChange: (state: any) => void;
}

/**
 * Search view component for sheet music with card rendering
 */
export function SheetMusicSearchView(props: SheetMusicSearchViewProps) {
  const appService = useService(AppService);
  const sheetMusicService = useService(SheetMusicService);

  const handleCardMount = (element: HTMLDivElement, item: SheetMusicDto) => {
    // Future: Could add context menu functionality here
    // const canApproveSheetMusic = appService().doesClientHaveSheetMusicFullAccess();
    // const canDelete = canApproveSheetMusic || appService().client().usertag == item.creatorUsername;
  };

  const renderSheetMusicCard = (idx: number) => (item: SheetMusicDto) => {
    const cardProps: GenericDisplayCardItem = {
      title: item.title,
      category: item.category,
      tooltip: !item.approved ? "This sheet music is not approved yet." : undefined,
      categoryColor: !item.approved ? "lightgray" : undefined,
      subtitle: item.creatorUsername,
      thumbnailImage: IMAGES.DEFAULT_SHEETMUSIC_BG_IMAGE,
      avatarImage: item.creatorUsername,
      badges: [
        <Badge
          __tooltip_title={`Difficulty Level: ${item.difficultyLevel}`}
          fontSize="8px !important"
          background={SheetMusicDtoHelpers.DifficultyLevelToColor(item.difficultyLevel)}
        >
          {item.difficultyLevel}
        </Badge>,
        ...GenericDefaultBadges(item as any),
      ],
      onMount: (element) => handleCardMount(element, item),
      onClick: () => {
        props.onActiveDetailChange(item.id);
        sheetMusicService().setActiveDetailID(item.id);
      }
    };

    return (
      <MotionFadeIn delay={0.1 * idx} duration={0.2}>
        <GenericDisplayCard item={cardProps} />
      </MotionFadeIn>
    );
  };

  const handleFacetLabelKeyMap = (key: string) => {
    return key.toLowerCase();
  };

  return (
    <Suspense>
      <SearchUIView<SheetMusicDto>
        config={props.config}
        initialSearchTerm={props.initialSearchTerm}
        defaultResultsPerPage={props.defaultResultsPerPage}
        amountList={props.resultsPerPageOptions}
        multiSelectEnabled={appService().doesClientHaveSheetMusicFullAccess()}
        onMultiSelect={props.onSelectedSheetsChange}
        onMount={(driver) => {
          sheetMusicService().setSearchDriver(driver);
        }}
        onRenderItem={renderSheetMusicCard}
        onDriverStateChange={props.onDriverStateChange}
        onFacetLabelKeyMap={handleFacetLabelKeyMap}
      />
    </Suspense>
  );
}
