import { Center, CircularProgress, Text, VStack } from "@hope-ui/solid";

interface SheetMusicLoadingProps {
  message?: string;
}

/**
 * Loading component for sheet music page
 */
export function SheetMusicLoading(props: SheetMusicLoadingProps) {
  return (
    <Center h="200px" w="100%">
      <VStack spacing="$4">
        <CircularProgress size="lg" />
        <Text color="$neutral11" fontSize="$sm">
          {props.message || "Loading sheet music..."}
        </Text>
      </VStack>
    </Center>
  );
}
