import { Alert, AlertDescription, AlertIcon, Alert<PERSON><PERSON>le, Button, VStack } from "@hope-ui/solid";
import { ErrorBoundary } from "solid-js";
import { JSXElement } from "solid-js";

interface SheetMusicErrorBoundaryProps {
  children: JSXElement;
  fallback?: (error: Error, reset: () => void) => JSXElement;
}

/**
 * Error boundary component for sheet music page
 */
export function SheetMusicErrorBoundary(props: SheetMusicErrorBoundaryProps) {
  const defaultFallback = (error: Error, reset: () => void) => (
    <VStack spacing="$4" p="$6" maxW="500px" mx="auto">
      <Alert status="danger">
        <AlertIcon />
        <VStack align="start" spacing="$2">
          <AlertTitle>Something went wrong!</AlertTitle>
          <AlertDescription>
            {error.message || "An unexpected error occurred while loading the sheet music page."}
          </AlertDescription>
        </VStack>
      </Alert>
      
      <Button onClick={reset} colorScheme="primary">
        Try Again
      </Button>
    </VStack>
  );

  return (
    <ErrorBoundary fallback={props.fallback || defaultFallback}>
      {props.children}
    </ErrorBoundary>
  );
}
