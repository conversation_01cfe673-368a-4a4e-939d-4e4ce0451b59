import { onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import CoreWasmService from "~/services/core-wasm.service";
import MidiPlayerService from "~/services/midi-player.service";
import NotificationService from "~/services/notification.service";
import { SheetMusicService } from "~/services/sheet-music.service";
import { TauriService } from "~/services/tauri.service";
import { AudioReverb, AudioReverbPreset, DEFAULT_SOUNDFONT } from "~/types/audio.types";
import { IDS } from "~/util/const.common";
import { MIDI } from "~/util/const.midi";

/**
 * Custom hook for handling sheet music page initialization and cleanup
 */
export function useSheetMusicInitialization() {
  const appService = useService(AppService);
  const audioService = useService(AudioService);
  const midiPlayerService = useService(MidiPlayerService);
  const sheetMusicService = useService(SheetMusicService);

  const initializeAudioSystem = async () => {
    try {
      await appService().loadCoreWasm(new CoreWasmService(), new TauriService());
      await audioService().initialize();
      await audioService().loadSoundfont(DEFAULT_SOUNDFONT, false, true);
      await appService().coreService()?.add_socket(MIDI.MIDI_SYNTH_SOCKET_ID);
      
      // Configure audio reverb
      const preset = AudioReverb.FromPreset(AudioReverbPreset.Church);
      audioService().setReverbLevel(preset.AUDIO_REVERB_LEVEL);
      audioService().setReverbRoomsize(preset.AUDIO_REVERB_ROOMSIZE);
      audioService().setReverbDamp(preset.AUDIO_REVERB_DAMP);
      audioService().setReverbWidth(preset.AUDIO_REVERB_WIDTH);

      // Hide loading notifications
      NotificationService.hide(IDS.AUDIO_INITIALIZATION);
      NotificationService.hide(IDS.LOADING_SOUNDFONT);
    } catch (error) {
      console.error("Failed to initialize audio system:", error);
      NotificationService.show({
        id: "audio-init-fail",
        type: "danger",
        duration: 10_000,
        title: "Audio Initialization Fail",
        closable: true,
        description: `Sorry, something went wrong trying to initialize the audio system.`
      });
      // Could emit error event or show user notification here
    }
  };

  onMount(async () => {
    // Initialize MIDI player
    midiPlayerService().initialize();
    
    // Initialize audio system
    await initializeAudioSystem();
    
    // Activate sheet music repository
    sheetMusicService().setRepoActive(true);
  });

  onCleanup(() => {
    // Cleanup sheet music service
    sheetMusicService().setRepoActive(false);
    sheetMusicService().setSearchDriver(undefined);

    // Cleanup other services
    audioService().onDisconnect();
    midiPlayerService().onDisconnect();
    appService().onDisconnect();
    sheetMusicService().onDisconnect();
  });

  return {
    // Could expose loading states or error states here if needed
  };
}
