import createStorage from '@solid-primitives/local-store';
import { createMemo, createSignal, onMount } from "solid-js";
import { useService } from "solid-services";
import AppService from "~/services/app.service";
import {
  BASE_SEARCH_CONFIG,
  DEFAULT_RESULTS_PER_PAGE,
  RESULTS_PER_PAGE_OPTIONS,
  STORAGE_KEY_RESULTS_PER_PAGE
} from "~/routes/sheet-music";

/**
 * Custom hook for managing sheet music search configuration and state
 */
//@ts-ignore
export function useSheetMusicSearch() {
  const appService = useService(AppService);

  const [defaultResultsPerPage, setDefaultResultsPerPage] = createSignal(DEFAULT_RESULTS_PER_PAGE);
  const [localStorageSettings, setLocalStorageSettings] = createStorage<{
    [STORAGE_KEY_RESULTS_PER_PAGE]: string;
  }>();

  // Memoized search configuration based on user permissions
  const searchConfig = createMemo(() => {
    let config = { ...BASE_SEARCH_CONFIG };
    let facets = { ...config.searchQuery?.facets };

    // Add favorites facet for all users
    facets.favorites = { type: "value" };

    // Add approval facet for users with full access
    if (appService().doesClientHaveSheetMusicFullAccess()) {
      facets.approved = { type: "bool" };
    }

    if (config.searchQuery) {
      config.searchQuery.facets = facets;
      (config.searchQuery as any).usertag = appService().client().usertag;
    }

    return config;
  });

  const loadStoredResultsPerPage = () => {
    const resultsPerPage = localStorageSettings[STORAGE_KEY_RESULTS_PER_PAGE];
    if (resultsPerPage) {
      try {
        const parsed = JSON.parse(resultsPerPage);
        const pageSize = parseInt(parsed);
        if (RESULTS_PER_PAGE_OPTIONS.includes(pageSize as any)) {
          setDefaultResultsPerPage(pageSize as any);
        }
      } catch (error) {
        console.warn("Failed to parse stored results per page:", error);
      }
    }
  };

  const saveResultsPerPage = (resultsPerPage: number) => {
    if (RESULTS_PER_PAGE_OPTIONS.includes(resultsPerPage as any)) {
      setLocalStorageSettings(STORAGE_KEY_RESULTS_PER_PAGE, JSON.stringify(resultsPerPage));
    }
  };

  onMount(() => {
    // Load stored results per page preference
    loadStoredResultsPerPage();
  });

  return {
    driverOptions: searchConfig,
    defaultResultsPerPage,
    saveResultsPerPage,
    RESULTS_PER_PAGE: RESULTS_PER_PAGE_OPTIONS
  };
}
